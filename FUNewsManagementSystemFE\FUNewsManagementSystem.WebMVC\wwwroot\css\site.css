/* Enhanced CSS for FU News Management System */

/* Root Variables for Consistent Theming */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-gray: #f8fafc;
    --medium-gray: #e2e8f0;
    --dark-gray: #1e293b;
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
}

.navbar {
    background: rgba(30, 41, 59, 0.95) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

    .navbar-brand:hover {
        transform: scale(1.05);
    }

.nav-link {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    margin: 0 0.5rem;
    border-radius: var(--border-radius);
    color: #ffffff !important;
    padding: 0.5rem 1rem;
}

    .nav-link::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 50%;
        width: 0;
        height: 2px;
        background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        transition: var(--transition);
        transform: translateX(-50%);
    }

    .nav-link:hover::before {
        width: 80%;
    }

    .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
    }

/* Main Container */
.container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    margin: 2rem auto;
    padding: 2rem;
    max-width: 1400px;
    min-height: calc(100vh - 200px);
}

/* Enhanced Headings */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

h1 {
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: 2rem;
    color: var(--primary-color);
}

/* Enhanced Buttons */
.btn {
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    padding: 0.75rem 1.5rem;
}

    .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: var(--transition);
    }

    .btn:hover::before {
        left: 100%;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #059669);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #dc2626);
    color: white;
}

.btn-warning {
    background: linear-gradient(45deg, var(--warning-color), #d97706);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, var(--info-color), #2563eb);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, var(--secondary-color), #475569);
    color: white;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
}

    .card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
    }

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    border: none;
    font-weight: 600;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Enhanced Tables */
.table {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: white;
}

    .table thead th {
        background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
        color: white;
        font-weight: 600;
        border: none;
        padding: 1.25rem;
    }

    .table tbody tr {
        transition: var(--transition);
    }

        .table tbody tr:hover {
            background: rgba(59, 130, 246, 0.05);
            transform: scale(1.005);
        }

    .table tbody td {
        padding: 1rem 1.25rem;
        border-color: var(--medium-gray);
        vertical-align: middle;
    }

/* Enhanced Forms */
.form-control, .form-select, .form-check-input {
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
    padding: 0.75rem;
}

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
        background: rgba(59, 130, 246, 0.02);
    }

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

/* Enhanced Search Input Group */
.input-group {
    box-shadow: var(--shadow-md);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: white;
}

    .input-group .form-control {
        border: none;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    .input-group .input-group-text {
        background: white;
        border: none;
        padding: 0.75rem 1rem;
    }

    .input-group .btn {
        border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
        padding: 0.75rem 1.5rem;
    }

/* Enhanced Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border: none;
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 700;
}

.modal-backdrop {
    backdrop-filter: blur(4px);
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(5px);
    padding: 1rem 1.5rem;
}

.alert-danger {
    background: rgba(239, 68, 68, 0.15);
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

.alert-success {
    background: rgba(16, 185, 129, 0.15);
    color: #065f46;
    border-left: 4px solid var(--success-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.15);
    color: #1e40af;
    border-left: 4px solid var(--info-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.15);
    color: #92400e;
    border-left: 4px solid var(--warning-color);
}

/* Enhanced Badges */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.bg-success {
    background: linear-gradient(45deg, var(--success-color), #059669) !important;
}

.bg-secondary {
    background: linear-gradient(45deg, var(--secondary-color), #475569) !important;
}

/* Enhanced Pagination */
.pagination .page-link {
    border: 2px solid var(--medium-gray);
    color: var(--primary-color);
    font-weight: 600;
    margin: 0 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    min-width: 48px;
    text-align: center;
}

    .pagination .page-link:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }

.pagination .page-item.active .page-link {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    border-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
}

.pagination .page-item.disabled .page-link {
    background: var(--light-gray);
    border-color: var(--medium-gray);
    color: var(--text-secondary);
    cursor: not-allowed;
}

/* Enhanced Footer */
.footer {
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(10px);
    color: rgba(255, 255, 255, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    padding: 2rem 0;
}

    .footer a {
        color: #3b82f6;
        text-decoration: none;
        transition: var(--transition);
    }

        .footer a:hover {
            color: #60a5fa;
        }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 24px;
        height: 24px;
        margin: -12px 0 0 -12px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1.5rem;
        border-radius: var(--border-radius);
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.5rem 1rem;
    }

    .pagination .page-link {
        padding: 0.5rem 1rem;
        min-width: 40px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --light-gray: #1e293b;
        --medium-gray: #334155;
    }

    .container {
        background: rgba(30, 41, 59, 0.95);
        color: var(--text-primary);
    }

    .card {
        background: rgba(30, 41, 59, 0.95);
        color: var(--text-primary);
    }

    .table {
        background: rgba(30, 41, 59, 0.8);
        color: var(--text-primary);
    }

        .table tbody td {
            border-color: var(--medium-gray);
        }

    .input-group .form-control,
    .input-group .input-group-text {
        background: rgba(30, 41, 59, 0.95);
        color: var(--text-primary);
    }
}

/* Utility Classes */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: var(--transition);
}

    .hover-lift:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
    }
