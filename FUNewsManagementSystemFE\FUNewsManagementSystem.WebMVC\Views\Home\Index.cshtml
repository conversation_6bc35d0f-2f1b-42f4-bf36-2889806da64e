﻿@model ListViewModel<NewsArticleViewModel>

@{
    ViewData["Title"] = "News Articles";
}

<!-- Hero Section -->
<div class="hero-gradient-bg">
    <div class="container-fluid">
        <div class="hero-content">
            <div class="row align-items-center min-vh-40">
                <div class="col-lg-8">
                    <div class="hero-text-area">
                        <h1 class="hero-title animated-gradient-text">News Feed</h1>
                        <p class="hero-subtitle">Discover the latest and most exciting news</p>
                        <div class="hero-stats">
                            <div class="stat-item">
                                <span class="stat-number">@Model.TotalRecords</span>
                                <span class="stat-label">Article</span>
                            </div>
                            <div class="stat-divider"></div>
                            <div class="stat-item">
                                <span class="stat-number">@Model.TotalPages</span>
                                <span class="stat-label">Page</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 text-end">
                    @if (User.<PERSON><PERSON>laim("Role", "1"))
                    {
                        <div class="action-area">
                            <a asp-action="Create" class="btn-social-primary">
                                <span class="btn-icon">+</span>
                                <span class="btn-text">Create News</span>
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search & Filter Section -->
<div class="search-section">
    <div class="container-fluid">
        <div class="search-card">
            <form asp-action="Index" method="get" id="searchForm">
                <div class="search-content">
                    <!-- Search Input -->
                    <div class="search-input-container">
                        <div class="search-input-wrapper">
                            <span class="search-icon">🔍</span>
                            <input type="text" name="searchQuery" value="@Model.SearchQuery" class="search-input" placeholder="Search articles, categories..." autocomplete="off" />
                            <button type="submit" class="search-btn" id="searchBtn">
                                <span class="search-btn-text">Search</span>
                            </button>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="sort-options">
                        <span class="sort-label">Sort by:</span>
                        <div class="sort-buttons">
                            <a asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                                { "searchQuery", Model.SearchQuery },
                                { "sortBy", "NewsTitle" },
                                { "sortOrder", Model.SortBy == "NewsTitle" && Model.SortOrder == "asc" ? "desc" : "asc" },
                                { "pageNumber", "1" },
                                { "pageSize", Model.PageSize.ToString() }
                            })" class="sort-btn @(Model.SortBy == "NewsTitle" ? "active" : "")">
                                <span class="sort-emoji">📝</span>
                                <span>Title</span>
                                @if (Model.SortBy == "NewsTitle")
                                {
                                    <span class="sort-arrow">@(Model.SortOrder == "asc" ? "↑" : "↓")</span>
                                }
                            </a>
                            <a asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                                { "searchQuery", Model.SearchQuery },
                                { "sortBy", "CreatedDate" },
                                { "sortOrder", Model.SortBy == "CreatedDate" && Model.SortOrder == "asc" ? "desc" : "asc" },
                                { "pageNumber", "1" },
                                { "pageSize", Model.PageSize.ToString() }
                            })" class="sort-btn @(Model.SortBy == "CreatedDate" ? "active" : "")">
                                <span class="sort-emoji">📅</span>
                                <span>Date</span>
                                @if (Model.SortBy == "CreatedDate")
                                {
                                    <span class="sort-arrow">@(Model.SortOrder == "asc" ? "↑" : "↓")</span>
                                }
                            </a>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="sortBy" value="@Model.SortBy" />
                <input type="hidden" name="sortOrder" value="@Model.SortOrder" />
                <input type="hidden" name="pageNumber" value="@Model.PageNumber" />
                <input type="hidden" name="pageSize" value="@Model.PageSize" />
            </form>
        </div>
    </div>
</div>

<!-- Loading Animation -->
<div id="loadingSpinner" class="loading-overlay d-none">
    <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">Searching...</p>
    </div>
</div>

<!-- Articles Grid -->
@if (Model.Item.Any())
{
    <div class="articles-section">
        <div class="container-fluid">
            <div class="articles-grid">
                @foreach (var article in Model.Item)
                {
                    <div class="article-card-wrapper">
                        <article class="article-card">
                            <div class="article-header">
                                <div class="category-badge">
                                    <span class="category-emoji">🏷️</span>
                                    <span class="category-name">@article.CategoryName</span>
                                </div>
                                <div class="status-indicator">
                                    <span class="status-dot @(article.NewsStatus ? "published" : "draft")"></span>
                                    <span class="status-text">@(article.NewsStatus ? "Published" : "Draft")</span>
                                </div>
                            </div>
                            <div class="article-content">
                                <h3 class="article-title">
                                    <a asp-action="Details" asp-route-id="@article.NewsArticleId" class="title-link">@article.NewsTitle</a>
                                </h3>
                                <p class="article-excerpt">
                                    @(article.Headline?.Length > 120 ? article.Headline.Substring(0, 120) + "..." : article.Headline)
                                </p>
                                @if (article.Tags.Any())
                                {
                                    <div class="article-tags">
                                        @foreach (var tag in article.Tags.Take(3))
                                        {
                                            <span class="tag-pill">#@tag.TagName</span>
                                        }
                                        @if (article.Tags.Count() > 3)
                                        {
                                            <span class="tag-more">+@(article.Tags.Count() - 3) more</span>
                                        }
                                    </div>
                                }
                                <div class="article-meta">
                                    <div class="meta-item">
                                        <span class="meta-emoji">📅</span>
                                        <span>@article.CreatedDate?.ToString("dd/MM/yyyy")</span>
                                    </div>
                                    <div class="meta-item">
                                        <span class="meta-emoji">🆔</span>
                                        <span>@article.NewsArticleId</span>
                                    </div>
                                </div>
                            </div>
                            <div class="article-actions">
                                <a asp-action="Details" asp-route-id="@article.NewsArticleId" class="action-btn view-btn">
                                    <span class="btn-emoji">👁️</span>
                                    <span>View</span>
                                </a>
                                @if (User.HasClaim("Role", "1"))
                                {
                                    <a asp-action="Edit" asp-route-id="@article.NewsArticleId" class="action-btn edit-btn">
                                        <span class="btn-emoji">✏️</span>
                                        <span>Edit</span>
                                    </a>
                                    <button class="action-btn delete-btn" data-modal-target="<EMAIL>">
                                        <span class="btn-emoji">🗑️</span>
                                        <span>Delete</span>
                                    </button>
                                }
                            </div>
                        </article>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Delete Modals -->
    @if (User.HasClaim("Role", "1"))
    {
        @foreach (var article in Model.Item)
        {
            <div class="modal" id="<EMAIL>">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Confirm Deletion</h5>
                            <button type="button" class="close-btn" data-close-modal>×</button>
                        </div>
                        <div class="modal-body">
                            <p>Are you sure you want to delete this post? "<strong>@article.NewsTitle</strong>"?</p>
                            <p class="warning-text">This action cannot be undone!</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="cancel-btn" data-close-modal>Cancel</button>
                            <form asp-action="Delete" method="post" style="display:inline;">
                                <input type="hidden" name="id" value="@article.NewsArticleId" />
                                <button type="submit" class="delete-btn">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
}
else
{
    <div class="empty-state">
        <div class="container-fluid">
            <div class="empty-content">
                <div class="empty-icon">📭</div>
                <h3 class="empty-title">No Articles Found</h3>
                <p class="empty-description">
                    @if (!string.IsNullOrEmpty(Model.SearchQuery))
                    {
                        <span>No articles match the keyword "<strong>@Model.SearchQuery</strong>"</span>
                    }
                    else
                    {
                        <span>No articles have been created yet.</span>
                    }
                </p>
                @if (User.HasClaim("Role", "1"))
                {
                    <a asp-action="Create" class="btn-social-primary">
                        <span class="btn-icon">+</span>
                        <span class="btn-text">Create First Article</span>
                    </a>
                }
            </div>
        </div>
    </div>
}

<!-- Pagination -->
@if (Model.TotalPages > 1)
{
    <div class="pagination-section">
        <div class="container-fluid">
            <nav class="modern-pagination">
                <div class="pagination-info">
                    <span>Showing @((Model.PageNumber - 1) * Model.PageSize + 1) - @(Math.Min(Model.PageNumber * Model.PageSize, Model.TotalRecords)) of @Model.TotalRecords articles</span>
                </div>
                <ul class="pagination-list">
                    <li class="pagination-item @(Model.PageNumber <= 1 ? "disabled" : "")">
                        <a class="pagination-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                            { "searchQuery", Model.SearchQuery },
                            { "sortBy", Model.SortBy },
                            { "sortOrder", Model.SortOrder },
                            { "pageNumber", (Model.PageNumber - 1).ToString() },
                            { "pageSize", Model.PageSize.ToString() }
                        })">
                            <span class="pagination-emoji">⬅️</span>
                            <span>Previous</span>
                        </a>
                    </li>
                    @{
                        var startPage = Math.Max(1, Model.PageNumber - 2);
                        var endPage = Math.Min(Model.TotalPages, startPage + 4);
                        if (endPage - startPage < 4)
                        {
                            startPage = Math.Max(1, endPage - 4);
                        }
                    }
                    @for (var i = startPage; i <= endPage; i++)
                    {
                        <li class="pagination-item @(i == Model.PageNumber ? "active" : "")">
                            <a class="pagination-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                                { "searchQuery", Model.SearchQuery },
                                { "sortBy", Model.SortBy },
                                { "sortOrder", Model.SortOrder },
                                { "pageNumber", i.ToString() },
                                { "pageSize", Model.PageSize.ToString() }
                            })">@i</a>
                        </li>
                    }
                    <li class="pagination-item @(Model.PageNumber >= Model.TotalPages ? "disabled" : "")">
                        <a class="pagination-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                            { "searchQuery", Model.SearchQuery },
                            { "sortBy", Model.SortBy },
                            { "sortOrder", Model.SortOrder },
                            { "pageNumber", (Model.PageNumber + 1).ToString() },
                            { "pageSize", Model.PageSize.ToString() }
                        })">
                            <span>Next</span>
                            <span class="pagination-emoji">➡️</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
}

<!-- Success Display -->
@if (!string.IsNullOrEmpty(TempData["Success"] as string))
{
    <div class="success-notification">
        <div class="container-fluid">
            <div class="success-content">
                <span class="success-icon">✅</span>
                <span class="success-text">@TempData["Success"]</span>
                <button class="success-close" onclick="this.parentElement.parentElement.remove()">❌</button>
            </div>
        </div>
    </div>
}

<!-- Error Display -->
@if (!string.IsNullOrEmpty(ViewBag.Error) || !string.IsNullOrEmpty(TempData["Error"] as string))
{
    <div class="error-notification">
        <div class="container-fluid">
            <div class="error-content">
                <span class="error-icon">⚠️</span>
                <span class="error-text">@(ViewBag.Error ?? TempData["Error"])</span>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">❌</button>
            </div>
        </div>
    </div>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Search form enhancements
            const searchForm = document.querySelector('#searchForm');
            const searchBtn = document.querySelector('#searchBtn');
            const searchInput = document.querySelector('input[name="searchQuery"]');

            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }

            searchForm.addEventListener('submit', function () {
                searchBtn.classList.add('loading');
                searchBtn.querySelector('.search-btn-text').textContent = 'Searching...';
                document.getElementById('loadingSpinner').classList.remove('d-none');
            });

            // Modal functionality
            const deleteButtons = document.querySelectorAll('.delete-btn[data-modal-target]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const modalId = this.getAttribute('data-modal-target');
                    const modal = document.getElementById(modalId);
                    modal.classList.add('active');
                });
            });

            const closeButtons = document.querySelectorAll('[data-close-modal]');
            closeButtons.forEach(button => {
                button.addEventListener('click', function () {
                    const modal = this.closest('.modal');
                    modal.classList.remove('active');
                });
            });

            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('click', function (e) {
                    if (e.target === this) {
                        this.classList.remove('active');
                    }
                });
            });

            // Auto-dismiss notifications
            setTimeout(() => {
                const successNotification = document.querySelector('.success-notification');
                const errorNotification = document.querySelector('.error-notification');

                if (successNotification) {
                    successNotification.style.opacity = '0';
                    setTimeout(() => successNotification.remove(), 300);
                }

                if (errorNotification) {
                    errorNotification.style.opacity = '0';
                    setTimeout(() => errorNotification.remove(), 300);
                }
            }, 5000);
        });
    </script>

    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            min-height: 100vh;
            margin: 0;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero-gradient-bg {
            background: linear-gradient(135deg, #667eea, #764ba2);
            position: relative;
            padding: 40px 0;
        }

        .hero-content {
            position: relative;
        }

        .min-vh-40 {
            min-height: 40vh;
        }

        .hero-text-area {
            position: relative;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            line-height: 1.1;
        }

        .animated-gradient-text {
            background: linear-gradient(45deg, #fff, #f0f8ff, #fff);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientAnimation 4s ease infinite;
        }

         gradientAnimation {
            0%

        {
            background-position: 0% 50%;
        }

        50% {
            background-position: 100% 50%;
        }

        100% {
            background-position: 0% 50%;
        }

        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .hero-stats {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            color: white;
        }

        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .stat-divider {
            width: 2px;
            height: 50px;
            background: rgba(255, 255, 255, 0.3);
        }

        /* Social Media Style Button */
        .btn-social-primary {
            display: inline-flex;
            align-items: center;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

            .btn-social-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 40px rgba(255, 107, 107, 0.4);
            }

        .btn-icon {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        /* Search Section */
        .search-section {
            margin: -2rem 0 3rem 0;
            position: relative;
        }

        .search-card {
            border-radius: 20px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .search-content {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .search-input-container {
            flex: 1;
        }

        .search-input-wrapper {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 50px;
            padding: 0.75rem;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

            .search-input-wrapper:focus-within {
                transform: scale(1.02);
                box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
            }

        .search-icon {
            font-size: 1.2rem;
            margin: 0 1rem;
            color: #666;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            font-size: 1.1rem;
            padding: 0.5rem 0;
            color: #333;
            outline: none;
        }

            .search-input::placeholder {
                color: #999;
            }

        .search-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

            .search-btn:hover {
                transform: scale(1.05);
                box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
            }

            .search-btn.loading {
                opacity: 0.7;
                pointer-events: none;
            }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sort-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            font-size: 1rem;
        }

        .sort-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .sort-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

            .sort-btn:hover {
                background: rgba(255, 255, 255, 0.3);
                color: white;
            }

            .sort-btn.active {
                background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                color: white;
                border-color: transparent;
            }

        .sort-emoji {
            font-size: 1rem;
        }

        .sort-arrow {
            font-weight: bold;
            font-size: 1.1rem;
        }

        /* Loading Overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

         spin {
            0%

        {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }

        }

        .loading-text {
            font-size: 1.1rem;
            font-weight: 500;
        }

        /* Articles Section */
        .articles-section {
            padding: 2rem 0;
        }

        .articles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .article-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
        }

        .article-header {
            padding: 1.25rem 1.5rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .category-emoji {
            font-size: 1rem;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

            .status-dot.published {
                background: #10b981;
            }

            .status-dot.draft {
                background: #f59e0b;
            }

        .status-text {
            color: #666;
        }

        .article-content {
            padding: 1.5rem;
        }

        .article-title {
            margin: 0 0 1rem 0;
            font-size: 1.4rem;
            font-weight: 700;
            line-height: 1.4;
        }

        .title-link {
            color: #2d3748;
            text-decoration: none;
            transition: color 0.3s ease;
        }

            .title-link:hover {
                color: #667eea;
            }

        .article-excerpt {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .article-tags {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .tag-pill {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            color: #667eea;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .tag-more {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-style: italic;
        }

        .article-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            color: #a0aec0;
            font-size: 0.85rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .meta-emoji {
            font-size: 0.9rem;
        }

        .article-actions {
            display: flex;
            gap: 0.75rem;
            padding: 0 1.5rem 1.5rem;
        }

        .action-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .view-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

            .view-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            }

        .edit-btn {
            background: linear-gradient(45deg, #48bb78, #38a169);
            color: white;
        }

            .edit-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
            }

        .delete-btn {
            background: linear-gradient(45deg, #f56565, #e53e3e);
            color: white;
        }

            .delete-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
            }

        .btn-emoji {
            font-size: 1rem;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

            .modal.active {
                display: block;
            }

        .modal-dialog {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ffffff;
            border-radius: 10px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .modal-content {
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            background: #f56565;
            color: white;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            position: relative;
        }

        .modal-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .close-btn {
            position: absolute;
            right: 15px;
            top: 15px;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: white;
            cursor: pointer;
        }

        .modal-body {
            padding: 20px;
            font-size: 1rem;
            color: #333;
        }

        .warning-text {
            color: #e53e3e;
            font-weight: 500;
            margin-top: 10px;
        }

        .modal-footer {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .cancel-btn, .delete-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
        }

        .cancel-btn {
            background: #e2e8f0;
            color: #4a5568;
        }

            .cancel-btn:hover {
                background: #cbd5e0;
            }

        .delete-btn {
            background: #f56565;
            color: white;
        }

            .delete-btn:hover {
                background: #e53e3e;
            }

        /* Empty State */
        .empty-state {
            padding: 4rem 0;
            text-align: center;
        }

        .empty-content {
            max-width: 500px;
            margin: 0 auto;
        }

        .empty-icon {
            font-size: 6rem;
            margin-bottom: 2rem;
            opacity: 0.7;
        }

        .empty-title {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
        }

        .empty-description {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        /* Pagination */
        .pagination-section {
            padding: 3rem 0;
        }

        .modern-pagination {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }

        .pagination-info {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.95rem;
            font-weight: 500;
        }

        .pagination-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 0.5rem;
            align-items: center;
        }

        .pagination-item {
            display: flex;
        }

        .pagination-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.15);
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            border-radius: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

            .pagination-link:hover {
                background: rgba(255, 255, 255, 0.25);
                color: white;
            }

        .pagination-item.active .pagination-link {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-color: transparent;
        }

        .pagination-item.disabled .pagination-link {
            opacity: 0.5;
            pointer-events: none;
        }

        .pagination-emoji {
            font-size: 0.9rem;
        }

        /* Error Notification */
        .success-notification, .error-notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            z-index: 9999;
            transition: all 0.3s ease;
        }

        .success-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(72, 187, 120, 0.95);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(72, 187, 120, 0.3);
        }

        .error-content {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(245, 101, 101, 0.95);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            box-shadow: 0 10px 40px rgba(245, 101, 101, 0.3);
        }

        .success-icon, .error-icon {
            font-size: 1.2rem;
        }

        .success-text, .error-text {
            font-weight: 500;
        }

        .success-close, .error-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1rem;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

            .success-close:hover, .error-close:hover {
                opacity: 1;
            }
    </style>
}