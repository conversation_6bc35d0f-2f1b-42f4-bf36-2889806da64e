﻿@model CreateCategoryViewModels

@{
    ViewData["Title"] = "Create Category";
}

<div class="container-fluid mt-4">
    <!-- Enhanced Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between flex-wrap">
                <div class="d-flex align-items-center mb-3 mb-md-0">
                    <div class="icon-circle me-3">
                        <i class="bi bi-plus-circle fs-4"></i>
                    </div>
                    <div>
                        <h1 class="gradient-text mb-1">Create New Category</h1>
                        <p class="text-muted mb-0">Add a new category to organize your content</p>
                    </div>
                </div>
                <div class="action-buttons">
                    <button type="button" class="btn btn-success btn-lg hover-lift pulse-animation"
                            data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                        <i class="bi bi-plus-circle-fill me-2"></i>Create New Category
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Info Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="stats-card animate__animated animate__fadeInUp">
                <div class="stats-icon">
                    <i class="bi bi-tags"></i>
                </div>
                <div class="stats-content">
                    <h4>Categories</h4>
                    <p>Organize your content efficiently</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <i class="bi bi-diagram-3"></i>
                </div>
                <div class="stats-content">
                    <h4>Hierarchy</h4>
                    <p>Create parent-child relationships</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="stats-icon">
                    <i class="bi bi-lightbulb"></i>
                </div>
                <div class="stats-content">
                    <h4>Easy Setup</h4>
                    <p>Simple and intuitive creation process</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Modal -->
    <div class="modal fade" id="createCategoryModal" tabindex="-1" aria-labelledby="createCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createCategoryModalLabel">Create New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <form asp-action="Create" method="post" id="createCategoryForm">
                        <!-- Category Name -->
                        <div class="mb-3">
                            <label asp-for="CategoryName" class="form-label">Category Name *</label>
                            <input asp-for="CategoryName" class="form-control" id="categoryName" required maxlength="100" />
                            <span asp-validation-for="CategoryName" class="text-danger"></span>
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label asp-for="CategoryDescription" class="form-label">Description</label>
                            <textarea asp-for="CategoryDescription" class="form-control" id="categoryDescription" rows="3" maxlength="500"></textarea>
                            <span asp-validation-for="CategoryDescription" class="text-danger"></span>
                        </div>

                        <!-- Parent Category -->
                        <div class="mb-3">
                            <label asp-for="ParentCategoryId" class="form-label">Parent Category</label>
                            <select asp-for="ParentCategoryId" class="form-select" id="parentCategory" asp-items="@ViewBag.Categories">
                                <option value="">-- No Parent Category --</option>
                            </select>
                            <span asp-validation-for="ParentCategoryId" class="text-danger"></span>
                            <div class="form-text">Leave empty to create a top-level category</div>
                        </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="createCategoryForm" class="btn btn-success">Create Category</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Error/Success Messages -->
    @if (!string.IsNullOrEmpty(ViewBag.Error))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Error!</strong> @ViewBag.Error
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(ViewBag.Success))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>Success!</strong> @ViewBag.Success
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }

    <!-- Help Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="help-section">
                <h3><i class="bi bi-question-circle me-2"></i>Need Help?</h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="help-item">
                            <i class="bi bi-info-circle text-primary"></i>
                            <h5>Category Names</h5>
                            <p>Choose descriptive and unique names for your categories</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="help-item">
                            <i class="bi bi-diagram-2 text-success"></i>
                            <h5>Hierarchy</h5>
                            <p>Use parent categories to create organized structures</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="help-item">
                            <i class="bi bi-lightbulb text-warning"></i>
                            <h5>Best Practices</h5>
                            <p>Keep descriptions clear and concise for better organization</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing simple category form...');

            const form = document.getElementById('createCategoryForm');
            const modal = document.getElementById('createCategoryModal');

            console.log('Form found:', form);
            console.log('Modal found:', modal);

            if (form) {
                form.addEventListener('submit', function(e) {
                    console.log('Form submitted');
                });
            }

            if (modal) {
                modal.addEventListener('shown.bs.modal', function() {
                    console.log('Modal shown');
                    const nameInput = document.getElementById('categoryName');
                    if (nameInput) {
                        nameInput.focus();
                    }
                });
            }

        });
    </script>

    <style>
        /* Custom styles for enhanced create view */
        .icon-circle {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, var(--success-color), #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            height: 100%;
        }

            .stats-card:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-lg);
            }

        .stats-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .stats-content h4 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .stats-content p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .modal-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }

            .progress-indicator::before {
                content: '';
                position: absolute;
                top: 20px;
                left: 20px;
                right: 20px;
                height: 2px;
                background: var(--medium-gray);
                z-index: 1;
            }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            flex: 1;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            background: var(--medium-gray);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            transition: var(--transition);
        }

        .step.active .step-icon {
            background: linear-gradient(45deg, var(--success-color), #059669);
        }

        .step-text {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .step.active .step-text {
            color: var(--success-color);
            font-weight: 600;
        }

        .form-step {
            display: none;
        }

            .form-step.active {
                display: block;
                animation: fadeInUp 0.3s ease-in-out;
            }

        .step-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .step-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .step-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-control:not(:placeholder-shown),
        .form-floating > .form-select {
            padding-top: 1.625rem;
            padding-bottom: 0.625rem;
        }

            .form-floating > .form-control:focus ~ label,
            .form-floating > .form-control:not(:placeholder-shown) ~ label,
            .form-floating > .form-select ~ label {
                opacity: 0.65;
                transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            }

        .validation-message {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }

        .form-control.is-valid,
        .form-select.is-valid {
            border-color: var(--success-color);
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25);
        }

        .help-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow-md);
        }

            .help-section h3 {
                color: var(--text-primary);
                margin-bottom: 1.5rem;
            }

        .help-item {
            text-align: center;
            padding: 1rem;
        }

            .help-item i {
                font-size: 2rem;
                margin-bottom: 1rem;
            }

            .help-item h5 {
                color: var(--text-primary);
                margin-bottom: 0.5rem;
            }

            .help-item p {
                color: var(--text-secondary);
                font-size: 0.9rem;
                margin: 0;
            }

        .action-buttons .btn {
            min-width: 180px;
        }

        .action-buttons {
            width: 100%;
        }

            .action-buttons .btn {
                width: 100%;
            }

        .progress-indicator {
            flex-direction: column;
            gap: 1rem;
        }

            .progress-indicator::before {
                display: none;
            }

        .stats-card {
            margin-bottom: 1rem;
        }

        }
    </style>
}