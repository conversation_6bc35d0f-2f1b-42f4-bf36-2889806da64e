﻿@model CreateCategoryViewModels

@{
    ViewData["Title"] = "Create Category";
}

<div class="container-fluid mt-4">
    <!-- Enhanced Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between flex-wrap">
                <div class="d-flex align-items-center mb-3 mb-md-0">
                    <div class="icon-circle me-3">
                        <i class="bi bi-plus-circle fs-4"></i>
                    </div>
                    <div>
                        <h1 class="gradient-text mb-1">Create New Category</h1>
                        <p class="text-muted mb-0">Add a new category to organize your content</p>
                    </div>
                </div>
                <div class="action-buttons">
                    <button type="button" class="btn btn-success btn-lg hover-lift pulse-animation"
                            data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                        <i class="bi bi-plus-circle-fill me-2"></i>Create New Category
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Info Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="stats-card animate__animated animate__fadeInUp">
                <div class="stats-icon">
                    <i class="bi bi-tags"></i>
                </div>
                <div class="stats-content">
                    <h4>Categories</h4>
                    <p>Organize your content efficiently</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <i class="bi bi-diagram-3"></i>
                </div>
                <div class="stats-content">
                    <h4>Hierarchy</h4>
                    <p>Create parent-child relationships</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="stats-icon">
                    <i class="bi bi-lightbulb"></i>
                </div>
                <div class="stats-content">
                    <h4>Easy Setup</h4>
                    <p>Simple and intuitive creation process</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Modal -->
    <div class="modal fade" id="createCategoryModal" tabindex="-1" aria-labelledby="createCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex align-items-center">
                        <div class="modal-icon me-3">
                            <i class="bi bi-plus-circle"></i>
                        </div>
                        <div>
                            <h5 class="modal-title mb-0" id="createCategoryModalLabel">Create New Category</h5>
                            <small class="text-light opacity-75">Fill in the details below to create a category</small>
                        </div>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <form asp-action="Create" method="post" id="createCategoryForm">
                        <!-- Progress Indicator -->
                        <div class="progress-indicator mb-4">
                            <div class="step active" data-step="1">
                                <div class="step-icon"><i class="bi bi-1-circle"></i></div>
                                <div class="step-text">Basic Info</div>
                            </div>
                            <div class="step" data-step="2">
                                <div class="step-icon"><i class="bi bi-2-circle"></i></div>
                                <div class="step-text">Details</div>
                            </div>
                            <div class="step" data-step="3">
                                <div class="step-icon"><i class="bi bi-3-circle"></i></div>
                                <div class="step-text">Hierarchy</div>
                            </div>
                        </div>

                        <!-- Step 1: Category Name -->
                        <div class="form-step active" data-step="1">
                            <div class="step-header mb-3">
                                <h6 class="step-title">Basic Information</h6>
                                <p class="step-description">Enter the category name and basic details</p>
                            </div>
                            <div class="form-floating mb-4">
                                <input asp-for="CategoryName" class="form-control form-control-lg" id="categoryName"
                                       placeholder="Enter category name" required maxlength="100" />
                                <label for="categoryName" class="form-label">
                                    <i class="bi bi-tag me-2"></i>Category Name *
                                </label>
                                <span asp-validation-for="CategoryName" class="text-danger validation-message"></span>
                                <div class="form-text">
                                    <span id="nameCounter">0</span>/100 characters
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Description -->
                        <div class="form-step" data-step="2">
                            <div class="step-header mb-3">
                                <h6 class="step-title">Category Details</h6>
                                <p class="step-description">Provide a description for this category</p>
                            </div>
                            <div class="form-floating mb-4">
                                <textarea asp-for="CategoryDescription" class="form-control" id="categoryDescription"
                                          placeholder="Enter category description" style="height: 120px;" maxlength="500"></textarea>
                                <label for="categoryDescription" class="form-label">
                                    <i class="bi bi-text-paragraph me-2"></i>Description (Optional)
                                </label>
                                <span asp-validation-for="CategoryDescription" class="text-danger validation-message"></span>
                                <div class="form-text">
                                    <span id="descCounter">0</span>/500 characters
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Parent Category -->
                        <div class="form-step" data-step="3">
                            <div class="step-header mb-3">
                                <h6 class="step-title">Category Hierarchy</h6>
                                <p class="step-description">Choose a parent category if needed</p>
                            </div>
                            <div class="form-floating mb-4">
                                <select asp-for="ParentCategoryId" class="form-select" id="parentCategory" asp-items="@ViewBag.Categories">
                                    <option value="">-- No Parent Category --</option>
                                </select>
                                <label for="parentCategory" class="form-label">
                                    <i class="bi bi-diagram-3 me-2"></i>Parent Category (Optional)
                                </label>
                                <span asp-validation-for="ParentCategoryId" class="text-danger validation-message"></span>
                                <div class="form-text">
                                    Leave empty to create a top-level category
                                </div>
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between pt-3 border-top">
                            <button type="button" class="btn btn-outline-secondary" id="prevBtn" style="display: none;">
                                <i class="bi bi-arrow-left me-2"></i>Previous
                            </button>
                            <div class="ms-auto d-flex gap-2">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="bi bi-x-circle me-2"></i>Cancel
                                </button>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Next<i class="bi bi-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="createBtn" style="display: none;">
                                    <i class="bi bi-check-circle me-2"></i>Create Category
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Error/Success Messages -->
    @if (!string.IsNullOrEmpty(ViewBag.Error))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Error!</strong> @ViewBag.Error
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(ViewBag.Success))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>Success!</strong> @ViewBag.Success
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }

    <!-- Help Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="help-section">
                <h3><i class="bi bi-question-circle me-2"></i>Need Help?</h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="help-item">
                            <i class="bi bi-info-circle text-primary"></i>
                            <h5>Category Names</h5>
                            <p>Choose descriptive and unique names for your categories</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="help-item">
                            <i class="bi bi-diagram-2 text-success"></i>
                            <h5>Hierarchy</h5>
                            <p>Use parent categories to create organized structures</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="help-item">
                            <i class="bi bi-lightbulb text-warning"></i>
                            <h5>Best Practices</h5>
                            <p>Keep descriptions clear and concise for better organization</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentStep = 1;
            const totalSteps = 3;

            const form = document.getElementById('createCategoryForm');
            const nextBtn = document.getElementById('nextBtn');
            const prevBtn = document.getElementById('prevBtn');
            const createBtn = document.getElementById('createBtn');

            // Character counters
            const nameInput = document.getElementById('categoryName');
            const descInput = document.getElementById('categoryDescription');
            const nameCounter = document.getElementById('nameCounter');
            const descCounter = document.getElementById('descCounter');

            // Update character counters
            nameInput.addEventListener('input', function() {
                nameCounter.textContent = this.value.length;
                if (this.value.length > 80) {
                    nameCounter.style.color = '#ef4444';
                } else {
                    nameCounter.style.color = '#64748b';
                }
            });

            descInput.addEventListener('input', function() {
                descCounter.textContent = this.value.length;
                if (this.value.length > 400) {
                    descCounter.style.color = '#ef4444';
                } else {
                    descCounter.style.color = '#64748b';
                }
            });

            // Step navigation
            function updateStep(step) {
                // Update progress indicator
                document.querySelectorAll('.progress-indicator .step').forEach((el, index) => {
                    if (index + 1 <= step) {
                        el.classList.add('active');
                    } else {
                        el.classList.remove('active');
                    }
                });

                // Show/hide form steps
                document.querySelectorAll('.form-step').forEach(el => {
                    el.classList.remove('active');
                });
                document.querySelector(`[data-step="${step}"]`).classList.add('active');

                // Update buttons
                if (step === 1) {
                    prevBtn.style.display = 'none';
                } else {
                    prevBtn.style.display = 'inline-block';
                }

                if (step === totalSteps) {
                    nextBtn.style.display = 'none';
                    createBtn.style.display = 'inline-block';
                } else {
                    nextBtn.style.display = 'inline-block';
                    createBtn.style.display = 'none';
                }
            }

            nextBtn.addEventListener('click', function() {
                if (validateCurrentStep()) {
                    if (currentStep < totalSteps) {
                        currentStep++;
                        updateStep(currentStep);
                    }
                }
            });

            prevBtn.addEventListener('click', function() {
                if (currentStep > 1) {
                    currentStep--;
                    updateStep(currentStep);
                }
            });

            function validateCurrentStep() {
                if (currentStep === 1) {
                    const nameInput = document.getElementById('categoryName');
                    if (!nameInput.value.trim()) {
                        nameInput.classList.add('is-invalid');
                        nameInput.focus();
                        return false;
                    } else {
                        nameInput.classList.remove('is-invalid');
                        nameInput.classList.add('is-valid');
                    }
                }
                return true;
            }

            // Form submission
            form.addEventListener('submit', function(e) {
                createBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating...';
                createBtn.disabled = true;
                createBtn.classList.add('loading');
            });

            // Modal events
            const modal = document.getElementById('createCategoryModal');
            modal.addEventListener('shown.bs.modal', function() {
                currentStep = 1;
                updateStep(currentStep);
                nameInput.focus();
            });

            modal.addEventListener('hidden.bs.modal', function() {
                // Reset form
                form.reset();
                document.querySelectorAll('.form-control, .form-select').forEach(el => {
                    el.classList.remove('is-valid', 'is-invalid');
                });
                nameCounter.textContent = '0';
                descCounter.textContent = '0';
                currentStep = 1;
                updateStep(currentStep);
            });

            // Enhanced form validation
            const inputs = form.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value.trim() && this.checkValidity()) {
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                    }
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('is-invalid') && this.checkValidity()) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    }
                });
            });

            // Auto-hide alerts
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert && alert.classList.contains('show')) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            });
        });
    </script>

    <style>
        /* Custom styles for enhanced create view */
        .icon-circle {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, var(--success-color), #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            height: 100%;
        }

            .stats-card:hover {
                transform: translateY(-4px);
                box-shadow: var(--shadow-lg);
            }

        .stats-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .stats-content h4 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .stats-content p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .modal-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }

            .progress-indicator::before {
                content: '';
                position: absolute;
                top: 20px;
                left: 20px;
                right: 20px;
                height: 2px;
                background: var(--medium-gray);
                z-index: 1;
            }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            flex: 1;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            background: var(--medium-gray);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
            margin-bottom: 0.5rem;
            transition: var(--transition);
        }

        .step.active .step-icon {
            background: linear-gradient(45deg, var(--success-color), #059669);
        }

        .step-text {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .step.active .step-text {
            color: var(--success-color);
            font-weight: 600;
        }

        .form-step {
            display: none;
        }

            .form-step.active {
                display: block;
                animation: fadeInUp 0.3s ease-in-out;
            }

        .step-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .step-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .step-description {
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-control:not(:placeholder-shown),
        .form-floating > .form-select {
            padding-top: 1.625rem;
            padding-bottom: 0.625rem;
        }

            .form-floating > .form-control:focus ~ label,
            .form-floating > .form-control:not(:placeholder-shown) ~ label,
            .form-floating > .form-select ~ label {
                opacity: 0.65;
                transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            }

        .validation-message {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }

        .form-control.is-valid,
        .form-select.is-valid {
            border-color: var(--success-color);
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25);
        }

        .help-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            box-shadow: var(--shadow-md);
        }

            .help-section h3 {
                color: var(--text-primary);
                margin-bottom: 1.5rem;
            }

        .help-item {
            text-align: center;
            padding: 1rem;
        }

            .help-item i {
                font-size: 2rem;
                margin-bottom: 1rem;
            }

            .help-item h5 {
                color: var(--text-primary);
                margin-bottom: 0.5rem;
            }

            .help-item p {
                color: var(--text-secondary);
                font-size: 0.9rem;
                margin: 0;
            }

        .action-buttons .btn {
            min-width: 180px;
        }

        .action-buttons {
            width: 100%;
        }

            .action-buttons .btn {
                width: 100%;
            }

        .progress-indicator {
            flex-direction: column;
            gap: 1rem;
        }

            .progress-indicator::before {
                display: none;
            }

        .stats-card {
            margin-bottom: 1rem;
        }

        }
    </style>
}