﻿@model ArticleHistoryViewModel

@{
    ViewData["Title"] = "News Article History";
}

<div class="container mt-4 fade-in">
    <!-- Enhanced Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center mb-3">
                <div class="icon-wrapper me-3">
                    <i class="bi bi-clock-history display-4 gradient-text"></i>
                </div>
                <div>
                    <h1 class="gradient-text mb-1">Article History</h1>
                    <p class="text-muted mb-0">Track and manage your news article publishing history</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Search Card -->
    <div class="card hover-lift mb-4 animate__animated animate__fadeInUp">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-search me-2"></i>Search Article History
            </h5>
        </div>
        <div class="card-body">
            <form asp-action="History" method="post" id="historyForm">
                <div class="row align-items-end">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label asp-for="AccountId" class="form-label">
                                <i class="bi bi-person-badge me-1"></i>Account ID
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-hash"></i>
                                </span>
                                <input asp-for="AccountId"
                                       type="number"
                                       class="form-control"
                                       required
                                       min="1"
                                       placeholder="Enter account ID..." />
                            </div>
                            <span asp-validation-for="AccountId" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary w-100 hover-lift">
                                <i class="bi bi-search me-2"></i>View History
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Error Message -->
    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger animate__animated animate__fadeInDown" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <strong>Error:</strong> @ViewBag.ErrorMessage
        </div>
    }

    <!-- Results Section -->
    @if (Model.Articles.Any())
    {
        <!-- Results Header -->
        <div class="card hover-lift mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">
                        <i class="bi bi-newspaper me-2"></i>Article History Results
                    </h5>
                    <small class="text-white-50">Account ID: @Model.AccountId</small>
                </div>
                <div class="badge bg-light text-dark fs-6">
                    @Model.Articles.Count() @(Model.Articles.Count() == 1 ? "Article" : "Articles")
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="row mb-4">
            @{
                var totalArticles = Model.Articles.Count();
                var activeArticles = Model.Articles.Count(a => a.NewsStatus);
                var inactiveArticles = totalArticles - activeArticles;
                var categories = Model.Articles.Where(a => !string.IsNullOrEmpty(a.CategoryName)).Select(a => a.CategoryName).Distinct().Count();
            }

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-primary text-white hover-lift animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon me-3">
                                <i class="bi bi-file-earmark-text display-6"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">@totalArticles</h3>
                                <small>Total Articles</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-success text-white hover-lift animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon me-3">
                                <i class="bi bi-check-circle display-6"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">@activeArticles</h3>
                                <small>Active Articles</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-secondary text-white hover-lift animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon me-3">
                                <i class="bi bi-pause-circle display-6"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">@inactiveArticles</h3>
                                <small>Inactive Articles</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card bg-info text-white hover-lift animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon me-3">
                                <i class="bi bi-tags display-6"></i>
                            </div>
                            <div>
                                <h3 class="mb-0">@categories</h3>
                                <small>Categories</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Filter and Sort Section -->
        <div class="card hover-lift mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-funnel"></i>
                            </span>
                            <input type="text" id="tableFilter" class="form-control" placeholder="Filter articles...">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select id="categoryFilter" class="form-select">
                            <option value="">All Categories</option>
                            @foreach (var category in Model.Articles.Where(a => !string.IsNullOrEmpty(a.CategoryName)).Select(a => a.CategoryName).Distinct().OrderBy(c => c))
                            {
                                <option value="@category">@category</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-4">
                        <select id="statusFilter" class="form-select">
                            <option value="">All Status</option>
                            <option value="active">Active Only</option>
                            <option value="inactive">Inactive Only</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Table -->
        <div class="card hover-lift animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-table me-2"></i>Article Details
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="articlesTable">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id">
                                    <i class="bi bi-hash me-1"></i>ID
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="title">
                                    <i class="bi bi-card-text me-1"></i>Title
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="headline">
                                    <i class="bi bi-megaphone me-1"></i>Headline
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="category">
                                    <i class="bi bi-bookmark me-1"></i>Category
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="author">
                                    <i class="bi bi-person me-1"></i>Author
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="date">
                                    <i class="bi bi-calendar me-1"></i>Created Date
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                                <th>
                                    <i class="bi bi-tags me-1"></i>Tags
                                </th>
                                <th class="sortable" data-column="status">
                                    <i class="bi bi-toggle-on me-1"></i>Status
                                    <i class="bi bi-chevron-expand sort-icon"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var article in Model.Articles.OrderByDescending(a => a.CreatedDate))
                            {
                                <tr class="article-row" data-animate="fadeInUp">
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            @(article.NewsArticleId ?? "N/A")
                                        </span>
                                    </td>
                                    <td>
                                        <div class="article-title">
                                            <strong>@(article.NewsTitle ?? "N/A")</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="article-headline text-muted">
                                            @(article.Headline ?? "N/A")
                                        </div>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(article.CategoryName))
                                        {
                                            <span class="badge bg-primary">
                                                <i class="bi bi-bookmark me-1"></i>@article.CategoryName
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">N/A</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-person-circle me-2 text-muted"></i>
                                            <span>@(article.AuthorName ?? "N/A")</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if (article.CreatedDate.HasValue)
                                        {
                                            <div class="date-info">
                                                <div class="fw-bold">@article.CreatedDate.Value.ToString("MMM dd, yyyy")</div>
                                                <small class="text-muted">@article.CreatedDate.Value.ToString("HH:mm")</small>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="text-muted">N/A</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="tags-container">
                                            @if (article.TagName != null && article.TagName.Any())
                                            {
                                                @foreach (var tag in article.TagName.Take(3))
                                                {
                                                    <span class="badge bg-light text-dark me-1 mb-1">
                                                        <i class="bi bi-tag me-1"></i>@tag
                                                    </span>
                                                }
                                                @if (article.TagName.Count() > 3)
                                                {
                                                    <span class="badge bg-secondary">+@(article.TagName.Count() - 3)</span>
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-muted">No tags</span>
                                            }
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge @(article.NewsStatus ? "bg-success" : "bg-secondary") status-badge">
                                            <i class="bi @(article.NewsStatus ? "bi-check-circle" : "bi-pause-circle") me-1"></i>
                                            @(article.NewsStatus ? "Active" : "Inactive")
                                        </span>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
    else if (Model.AccountId > 0)
    {
        <!-- Enhanced No Results Message -->
        <div class="card hover-lift animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
            <div class="card-body text-center py-5">
                <div class="empty-state">
                    <i class="bi bi-inbox display-1 text-muted mb-3"></i>
                    <h4 class="text-muted mb-2">No Articles Found</h4>
                    <p class="text-muted mb-4">
                        No articles were found for Account ID: <strong>@Model.AccountId</strong>
                    </p>
                    <button type="button" class="btn btn-primary hover-lift" onclick="document.getElementById('historyForm').reset(); document.querySelector('input[name=AccountId]').focus();">
                        <i class="bi bi-arrow-left me-2"></i>Search Again
                    </button>
                </div>
            </div>
        </div>
    }
</div>

<!-- Custom Styles -->
<style>
    .icon-wrapper {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .stats-card {
        border: none;
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        position: relative;
    }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

    .stats-icon {
        opacity: 0.8;
    }

    .article-title {
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .article-headline {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.9rem;
    }

    .tags-container {
        max-width: 150px;
    }

    .date-info {
        min-width: 100px;
    }

    .status-badge {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }

    .sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: var(--transition);
    }

        .sortable:hover {
            background: rgba(59, 130, 246, 0.1);
        }

    .sort-icon {
        font-size: 0.7rem;
        opacity: 0.5;
        transition: var(--transition);
    }

    .sortable.sort-asc .sort-icon::before {
        content: "\f143";
    }

    .sortable.sort-desc .sort-icon::before {
        content: "\f140";
    }

    .empty-state i {
        animation: float 3s ease-in-out infinite;
    }

    .article-headline {
        max-width: 120px;
    }

    .tags-container {
        max-width: 100px;
    }

    .stats-card .display-6 {
        font-size: 2rem;
    }

    }
</style>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced table filtering
            const tableFilter = document.getElementById('tableFilter');
            const categoryFilter = document.getElementById('categoryFilter');
            const statusFilter = document.getElementById('statusFilter');
            const table = document.getElementById('articlesTable');

            function filterTable() {
                const searchTerm = tableFilter?.value.toLowerCase() || '';
                const categoryTerm = categoryFilter?.value.toLowerCase() || '';
                const statusTerm = statusFilter?.value.toLowerCase() || '';

                if (table) {
                    const rows = table.querySelectorAll('tbody tr');

                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        const categoryMatch = !categoryTerm || text.includes(categoryTerm);
                        const searchMatch = !searchTerm || text.includes(searchTerm);

                        let statusMatch = true;
                        if (statusTerm) {
                            const statusBadge = row.querySelector('.status-badge');
                            const isActive = statusBadge?.textContent.toLowerCase().includes('active');
                            statusMatch = (statusTerm === 'active' && isActive) ||
                                         (statusTerm === 'inactive' && !isActive);
                        }

                        if (searchMatch && categoryMatch && statusMatch) {
                            row.style.display = '';
                            row.classList.add('animate__animated', 'animate__fadeIn');
                        } else {
                            row.style.display = 'none';
                            row.classList.remove('animate__animated', 'animate__fadeIn');
                        }
                    });
                }
            }

            // Event listeners for filters
            tableFilter?.addEventListener('input', filterTable);
            categoryFilter?.addEventListener('change', filterTable);
            statusFilter?.addEventListener('change', filterTable);

            // Enhanced table sorting
            const sortableHeaders = document.querySelectorAll('.sortable');
            let currentSort = { column: null, direction: 'asc' };

            sortableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const tbody = table?.querySelector('tbody');

                    if (!tbody) return;

                    // Update sort direction
                    if (currentSort.column === column) {
                        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSort.direction = 'asc';
                    }
                    currentSort.column = column;

                    // Update header styles
                    sortableHeaders.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });
                    this.classList.add(`sort-${currentSort.direction}`);

                    // Sort rows
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    rows.sort((a, b) => {
                        let aVal, bVal;

                        switch(column) {
                            case 'id':
                                aVal = parseInt(a.cells[0].textContent.trim()) || 0;
                                bVal = parseInt(b.cells[0].textContent.trim()) || 0;
                                break;
                            case 'title':
                                aVal = a.cells[1].textContent.trim().toLowerCase();
                                bVal = b.cells[1].textContent.trim().toLowerCase();
                                break;
                            case 'headline':
                                aVal = a.cells[2].textContent.trim().toLowerCase();
                                bVal = b.cells[2].textContent.trim().toLowerCase();
                                break;
                            case 'category':
                                aVal = a.cells[3].textContent.trim().toLowerCase();
                                bVal = b.cells[3].textContent.trim().toLowerCase();
                                break;
                            case 'author':
                                aVal = a.cells[4].textContent.trim().toLowerCase();
                                bVal = b.cells[4].textContent.trim().toLowerCase();
                                break;
                            case 'date':
                                aVal = new Date(a.cells[5].textContent.trim());
                                bVal = new Date(b.cells[5].textContent.trim());
                                break;
                            case 'status':
                                aVal = a.cells[7].textContent.trim().toLowerCase();
                                bVal = b.cells[7].textContent.trim().toLowerCase();
                                break;
                            default:
                                return 0;
                        }

                        if (aVal < bVal) return currentSort.direction === 'asc' ? -1 : 1;
                        if (aVal > bVal) return currentSort.direction === 'asc' ? 1 : -1;
                        return 0;
                    });

                    // Re-append sorted rows
                    rows.forEach(row => tbody.appendChild(row));
                });
            });

            // Animate table rows on load
            setTimeout(() => {
                const rows = document.querySelectorAll('.article-row');
                rows.forEach((row, index) => {
                    setTimeout(() => {
                        row.classList.add('animate__animated', 'animate__fadeInUp');
                    }, index * 50);
                });
            }, 500);
        });
    </script>
}