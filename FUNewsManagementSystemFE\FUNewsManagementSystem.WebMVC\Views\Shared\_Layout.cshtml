﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - FUNewsManagementSystem</title>

    <!-- Enhanced Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Enhanced Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom Enhanced Styles -->
    <link rel="stylesheet" href="~/css/enhanced-site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/FUNewsManagementSystem.WebMVC.styles.css" asp-append-version="true" />
</head>
<body>
    <!-- Enhanced Header with Glass Effect -->
    <header class="sticky-top">
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container-fluid">
                <a class="navbar-brand animate__animated animate__fadeInLeft" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-newspaper me-2"></i>FU News Hub
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse"
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1 animate__animated animate__fadeInUp">
                        <li class="nav-item">
                            <a class="nav-link text-light" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-house-door me-1"></i>Home
                            </a>
                        </li>
                        @if (User.HasClaim("Role", "3")) // Admin
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-gear me-1"></i>Admin
                                </a>
                                <ul class="dropdown-menu glass-effect">
                                    <li>
                                        <a class="dropdown-item" asp-area="" asp-controller="SystemAccount" asp-action="Index">
                                            <i class="bi bi-people me-2"></i>System Accounts
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" asp-area="" asp-controller="Report" asp-action="Index">
                                            <i class="bi bi-graph-up me-2"></i>Reports
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        }
                        @if (User.HasClaim("Role", "2")) // Lecturer
                        {
                            <li class="nav-item">
                                <a class="nav-link text-light" href="#">
                                    <i class="bi bi-mortarboard me-1"></i>Lecturer Panel
                                </a>
                            </li>
                        }
                        @if (User.HasClaim("Role", "1")) // Staff
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-pencil-square me-1"></i>Content
                                </a>
                                <ul class="dropdown-menu glass-effect">
                                    <li>
                                        <a class="dropdown-item" asp-area="" asp-controller="Category" asp-action="Index">
                                            <i class="bi bi-tags me-2"></i>Categories
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" asp-area="" asp-controller="Home" asp-action="History">
                                            <i class="bi bi-clock-history me-2"></i>History
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        }
                        <li class="nav-item">
                            <a class="nav-link text-light" asp-area="" asp-controller="Home" asp-action="Privacy">
                                <i class="bi bi-shield-lock me-1"></i>Privacy
                            </a>
                        </li>
                    </ul>
                    <div class="navbar-nav animate__animated animate__fadeInRight">
                        @if (User.Identity.IsAuthenticated)
                        {
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle text-light" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle me-1"></i>@User.Identity.Name
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end glass-effect">
                                    <li><h6 class="dropdown-header">Welcome back!</h6></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Auth" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <a class="nav-link text-light" asp-area="" asp-controller="Auth" asp-action="Login">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Login
                            </a>
                        }
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Enhanced Main Content -->
    <div class="container fade-in">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <!-- Enhanced Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>

    <!-- Enhanced JavaScript -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading states to forms
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.classList.add('loading');
                    submitBtn.disabled = true;
                }
            });
        });

        // Toast notifications for alerts
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }

        // Enhanced table interactions
        document.querySelectorAll('.table tbody tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
            });
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.card, .table, .alert').forEach(el => {
            observer.observe(el);
        });

        // Enhanced search functionality
        const searchInputs = document.querySelectorAll('input[type="text"][placeholder*="Search"]');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                this.classList.add('loading');
                timeout = setTimeout(() => {
                    this.classList.remove('loading');
                }, 500);
            });
        });
    </script>

    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

    <!-- Toast Container for Notifications -->
    <div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>
</body>
</html>