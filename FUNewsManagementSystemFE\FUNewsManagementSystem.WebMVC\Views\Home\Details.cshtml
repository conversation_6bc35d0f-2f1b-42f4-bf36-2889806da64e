﻿@model NewsDetailViewModel

@{
    ViewData["Title"] = "Article Details";
}

<!-- Hero Section with Dynamic Background -->
<div class="hero-container">
    <div class="hero-background">
        <div class="animated-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
    </div>
    <div class="hero-content">
        <div class="hero-badge fade-in">
            <i class="bi bi-newspaper"></i>
            <span>@Model.Category?.CategoryName</span>
        </div>
        <h1 class="hero-title fade-in" data-delay="0.2">
            @Model.NewsTitle
        </h1>
        <p class="hero-subtitle fade-in" data-delay="0.4">
            @Model.Headline
        </p>
        <div class="hero-meta fade-in" data-delay="0.6">
            <div class="meta-item">
                <i class="bi bi-person-circle"></i>
                <span>@Model.CreatedBy?.AccountName</span>
            </div>
            <div class="meta-item">
                <i class="bi bi-calendar3"></i>
                <span>@Model.CreatedDate?.ToString("MMM dd, yyyy")</span>
            </div>
            <div class="meta-item">
                <i class="bi bi-eye"></i>
                <span>@Model.NewsSource</span>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Layout -->
<div class="content-layout">
    <div class="container-fluid">
        <div class="row g-4">
            <!-- Article Content -->
            <div class="col-xl-8 col-lg-7">
                <div class="article-card glassmorphism fade-in" data-delay="0.8">
                    <div class="article-header">
                        <div class="status-indicator">
                            <div class="status-dot @(Model.NewsStatus == true ? "published" : "draft")"></div>
                            <span class="status-text">@(Model.NewsStatus == true ? "Published" : "Draft")</span>
                        </div>
                        <div class="article-actions">
                            @if (User.HasClaim("Role", "1"))
                            {
                                <button class="action-btn edit-btn" onclick="location.href='@Url.Action("Edit", new { id = Model.NewsArticleId })'">
                                    <i class="bi bi-pencil"></i>
                                    <span>Edit</span>
                                </button>
                                <button class="action-btn delete-btn" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                    <i class="bi bi-trash"></i>
                                    <span>Delete</span>
                                </button>
                            }
                        </div>
                    </div>

                    <div class="article-content">
                        @Html.Raw(Model.NewsContent)
                    </div>

                    <!-- Tags Section -->
                    @if (Model.Tags?.Any() ?? false)
                    {
                        <div class="tags-section">
                            <h6 class="tags-title">
                                <i class="bi bi-tags"></i>
                                Tags
                            </h6>
                            <div class="tags-container">
                                @foreach (var tag in Model.Tags)
                                {
                                    <span class="tag-pill">
                                        <i class="bi bi-hash"></i>
                                        @tag.TagName
                                    </span>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-xl-4 col-lg-5">
                <div class="sidebar-sticky">
                    <!-- Article Info Card -->
                    <div class="info-card glassmorphism fade-in" data-delay="1.0">
                        <div class="info-header">
                            <i class="bi bi-info-circle"></i>
                            <h5>Article Information</h5>
                        </div>
                        <div class="info-content">
                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="bi bi-building"></i>
                                </div>
                                <div class="info-details">
                                    <label>Source</label>
                                    <span>@Model.NewsSource</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="bi bi-folder"></i>
                                </div>
                                <div class="info-details">
                                    <label>Category</label>
                                    <span>@Model.Category?.CategoryName</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="bi bi-person-badge"></i>
                                </div>
                                <div class="info-details">
                                    <label>Author</label>
                                    <span>@Model.CreatedBy?.AccountName</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <i class="bi bi-calendar-plus"></i>
                                </div>
                                <div class="info-details">
                                    <label>Created</label>
                                    <span>@Model.CreatedDate?.ToString("MMM dd, yyyy HH:mm")</span>
                                </div>
                            </div>

                            @if (Model.ModifiedDate != null)
                            {
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <div class="info-details">
                                        <label>Modified</label>
                                        <span>@Model.ModifiedDate?.ToString("MMM dd, yyyy HH:mm")</span>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Quick Actions Card -->
                    <div class="actions-card glassmorphism fade-in" data-delay="1.2">
                        <div class="actions-header">
                            <i class="bi bi-lightning"></i>
                            <h5>Quick Actions</h5>
                        </div>
                        <div class="actions-content">
                            <button class="quick-action-btn share-btn">
                                <div class="action-icon">
                                    <i class="bi bi-share"></i>
                                </div>
                                <div class="action-text">
                                    <span class="action-title">Share Article</span>
                                    <span class="action-desc">Share on social media</span>
                                </div>
                            </button>

                            <button class="quick-action-btn print-btn" onclick="window.print()">
                                <div class="action-icon">
                                    <i class="bi bi-printer"></i>
                                </div>
                                <div class="action-text">
                                    <span class="action-title">Print Article</span>
                                    <span class="action-desc">Print or save as PDF</span>
                                </div>
                            </button>

                            <button class="quick-action-btn bookmark-btn">
                                <div class="action-icon">
                                    <i class="bi bi-bookmark"></i>
                                </div>
                                <div class="action-text">
                                    <span class="action-title">Bookmark</span>
                                    <span class="action-desc">Save for later</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="floating-actions">
    <button class="fab main-fab" onclick="location.href='@Url.Action("Index")'">
        <i class="bi bi-arrow-left"></i>
        <span class="fab-tooltip">Back to List</span>
    </button>
</div>

<!-- Enhanced Delete Modal -->
@if (User.HasClaim("Role", "1"))
{
    <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modern-modal">
                <div class="modal-header danger-header">
                    <div class="modal-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="modal-title-section">
                        <h5 class="modal-title">Delete Article</h5>
                        <p class="modal-subtitle">This action cannot be undone</p>
                    </div>
                    <button type="button" class="btn-close-modern" data-bs-dismiss="modal">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation">
                        <div class="confirmation-icon">
                            <i class="bi bi-trash"></i>
                        </div>
                        <h6 class="confirmation-title">Delete "@Model.NewsTitle"?</h6>
                        <p class="confirmation-message">
                            Are you sure you want to delete this article? This action will permanently remove
                            the article and all associated data from the system.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary-modern" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i>
                        Cancel
                    </button>
                    <form asp-action="Delete" method="post" class="d-inline">
                        <input type="hidden" name="id" value="@Model.NewsArticleId" />
                        <button type="submit" class="btn btn-danger-modern">
                            <i class="bi bi-trash"></i>
                            Delete Article
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}

<!-- Error Alert -->
@if (TempData["Error"] != null)
{
    <div class="floating-alert error-alert">
        <div class="alert-icon">
            <i class="bi bi-exclamation-triangle-fill"></i>
        </div>
        <div class="alert-content">
            <strong>Error:</strong> @TempData["Error"]
        </div>
        <button class="alert-close" onclick="this.parentElement.remove()">
            <i class="bi bi-x"></i>
        </button>
    </div>
}

@section Scripts {
    <style>
        /* Hero Section Styles */
        .hero-container {
            position: relative;
            height: 60vh;
            min-height: 500px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: -2rem -2rem 3rem -2rem;
            border-radius: 0 0 2rem 2rem;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(147, 51, 234, 0.9) 50%, rgba(239, 68, 68, 0.9) 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        .animated-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 20s infinite linear;
        }

        .shape-1 {
            width: 150px;
            height: 150px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
            animation-duration: 25s;
        }

        .shape-2 {
            width: 100px;
            height: 100px;
            top: 60%;
            right: 15%;
            animation-delay: -5s;
            animation-duration: 20s;
        }

        .shape-3 {
            width: 200px;
            height: 200px;
            bottom: 10%;
            left: 20%;
            animation-delay: -10s;
            animation-duration: 30s;
        }

        .shape-4 {
            width: 80px;
            height: 80px;
            top: 10%;
            right: 30%;
            animation-delay: -15s;
            animation-duration: 18s;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
            max-width: 800px;
            padding: 2rem;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.2;
            margin-bottom: 1rem;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            font-weight: 400;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Content Layout */
        .content-layout {
            margin-top: 2rem;
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        /* Article Card */
        .article-card {
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .article-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

            .status-dot.published {
                background: #10b981;
            }

            .status-dot.draft {
                background: #f59e0b;
            }


        .status-text {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .article-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .edit-btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }

            .edit-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            }

        .delete-btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

            .delete-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
            }

        .article-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #374151;
            margin-bottom: 2rem;
        }

            .article-content p {
                margin-bottom: 1.5rem;
            }

            .article-content h1,
            .article-content h2,
            .article-content h3 {
                margin-top: 2rem;
                margin-bottom: 1rem;
                color: #1f2937;
            }

        /* Tags Section */
        .tags-section {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .tags-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            color: #374151;
            font-weight: 600;
        }

        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        .tag-pill {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            color: #374151;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

            .tag-pill:hover {
                background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

        /* Sidebar */
        .sidebar-sticky {
            position: sticky;
            top: 2rem;
        }

        .info-card, .actions-card {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .info-header, .actions-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            color: #1f2937;
        }

            .info-header h5, .actions-header h5 {
                margin: 0;
                font-weight: 700;
            }

        .info-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

            .info-item:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }

        .info-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 1.1rem;
        }

        .info-details {
            flex: 1;
        }

            .info-details label {
                display: block;
                font-size: 0.85rem;
                font-weight: 600;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .info-details span {
                font-size: 0.95rem;
                color: #374151;
                font-weight: 500;
            }

        /* Quick Actions */
        .quick-action-btn {
            display: flex;
            align-items: center;
            gap: 1rem;
            width: 100%;
            padding: 1rem;
            background: transparent;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

            .quick-action-btn:hover {
                background: rgba(59, 130, 246, 0.05);
                border-color: #3b82f6;
                transform: translateX(4px);
            }

        .action-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .action-text {
            flex: 1;
            text-align: left;
        }

        .action-title {
            display: block;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .action-desc {
            display: block;
            font-size: 0.85rem;
            color: #6b7280;
        }

        /* Floating Action Button */
        .floating-actions {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            z-index: 1000;
        }

        .fab {
            position: relative;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.25rem;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

            .fab:hover {
                transform: scale(1.1);
                box-shadow: 0 12px 30px rgba(59, 130, 246, 0.6);
            }

        .fab-tooltip {
            position: absolute;
            left: calc(100% + 1rem);
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.85rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .fab:hover .fab-tooltip {
            opacity: 1;
        }

        /* Enhanced Modal */
        .modern-modal {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .danger-header {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 1.5rem;
            border: none;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .modal-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .modal-title-section h5 {
            margin: 0;
            font-weight: 700;
        }

        .modal-subtitle {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .btn-close-modern {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: auto;
        }

            .btn-close-modern:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: rotate(90deg);
            }

        .delete-confirmation {
            text-align: center;
            padding: 1rem;
        }

        .confirmation-icon {
            width: 80px;
            height: 80px;
            background: rgba(239, 68, 68, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: #ef4444;
            font-size: 2rem;
        }

        .confirmation-title {
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .confirmation-message {
            color: #6b7280;
            line-height: 1.6;
        }

        .btn-secondary-modern, .btn-danger-modern {
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-secondary-modern {
            background: #f3f4f6;
            color: #374151;
        }

            .btn-secondary-modern:hover {
                background: #e5e7eb;
                transform: translateY(-2px);
            }

        .btn-danger-modern {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

            .btn-danger-modern:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
            }

        /* Floating Alert */
        .floating-alert {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(239, 68, 68, 0.95);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 1rem;
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
            display: flex;
            align-items: center;
            gap: 1rem;
            z-index: 1050;
            backdrop-filter: blur(10px);
            animation: slideInRight 0.5s ease;
        }


        .alert-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

            .alert-close:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: rotate(90deg);
            }

        /* Animation Classes */
        .fade-in {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }

            .fade-in[data-delay="0.2"] {
                animation-delay: 0.2s;
            }

            .fade-in[data-delay="0.4"] {
                animation-delay: 0.4s;
            }

            .fade-in[data-delay="0.6"] {
                animation-delay: 0.6s;
            }

            .fade-in[data-delay="0.8"] {
                animation-delay: 0.8s;
            }

            .fade-in[data-delay="1.0"] {
                animation-delay: 1.0s;
            }

            .fade-in[data-delay="1.2"] {
                animation-delay: 1.2s;
            }

 

        /* Responsive Design */
        {
            .hero-container

        {
            height: 50vh;
            min-height: 400px;
            margin: -1rem -1rem 2rem -1rem;
        }

        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .hero-meta {
            flex-direction: column;
            gap: 1rem;
        }

        .meta-item {
            justify-content: center;
        }

        .content-layout .container-fluid {
            padding: 0;
        }

        .col-xl-8, .col-xl-4 {
            padding: 0 1rem;
        }

        .article-card, .info-card, .actions-card {
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-radius: 1rem;
        }

        .article-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .article-actions {
            width: 100%;
            justify-content: space-between;
        }

        .action-btn {
            flex: 1;
            justify-content: center;
        }

        .floating-actions {
            bottom: 1rem;
            left: 1rem;
        }

        .fab-tooltip {
            display: none;
        }

        .floating-alert {
            top: 1rem;
            right: 1rem;
            left: 1rem;
            right: auto;
        }

        .quick-action-btn:hover {
            transform: none;
        }

        .sidebar-sticky {
            position: static;
        }

        }

       {
            .hero-title

        {
            font-size: 2rem;
        }

        .hero-subtitle {
            font-size: 1rem;
        }

        .tags-container {
            gap: 0.5rem;
        }

        .tag-pill {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }

        .article-content {
            font-size: 1rem;
        }

        .info-item {
            gap: 0.75rem;
        }

        .info-icon, .action-icon {
            width: 36px;
            height: 36px;
            font-size: 1rem;
        }

        }

        /* Print Styles */
         print {
            .hero-background, .animated-shapes, .floating-actions, .article-actions, .actions-card, .floating-alert

        {
            display: none !important;
        }

        .hero-container {
            height: auto;
            background: none;
            color: black;
            padding: 2rem 0;
        }

        .glassmorphism {
            background: white;
            box-shadow: none;
            border: 1px solid #e5e7eb;
        }

        .article-content {
            color: black;
        }

        }

        /* Dark Mode Support */
         (prefers-color-scheme: dark) {
            .glassmorphism

        {
            background: rgba(30, 41, 59, 0.95);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .article-content,
        .info-details span,
        .action-title {
            color: #f8fafc;
        }

        .info-details label,
        .action-desc {
            color: #cbd5e1;
        }

        .status-text {
            color: #f8fafc;
        }

        .info-icon {
            background: rgba(71, 85, 105, 0.5);
            color: #cbd5e1;
        }

        .quick-action-btn {
            border-color: rgba(255, 255, 255, 0.1);
        }

            .quick-action-btn:hover {
                background: rgba(59, 130, 246, 0.1);
            }

        .tag-pill {
            background: rgba(71, 85, 105, 0.5);
            color: #f8fafc;
            border-color: rgba(255, 255, 255, 0.1);
        }

        }

        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Focus States for Accessibility */
        .action-btn:focus,
        .quick-action-btn:focus,
        .fab:focus,
        .btn-close-modern:focus,
        .alert-close:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* Loading State */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

         spin {
            0%

        {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }

        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Auto-hide floating alerts
            const alerts = document.querySelectorAll('.floating-alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.animation = 'slideOutRight 0.5s ease forwards';
                    setTimeout(() => alert.remove(), 500);
                }, 5000);
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add bookmark functionality
            const bookmarkBtn = document.querySelector('.bookmark-btn');
            if (bookmarkBtn) {
                bookmarkBtn.addEventListener('click', function() {
                    this.classList.toggle('bookmarked');
                    const icon = this.querySelector('i');
                    if (this.classList.contains('bookmarked')) {
                        icon.className = 'bi bi-bookmark-fill';
                        showToast('Article bookmarked!', 'success');
                    } else {
                        icon.className = 'bi bi-bookmark';
                        showToast('Bookmark removed!', 'info');
                    }
                });
            }

            // Add share functionality
            const shareBtn = document.querySelector('.share-btn');
            if (shareBtn) {
                shareBtn.addEventListener('click', async function() {
                    if (navigator.share) {
                        try {
                            await navigator.share({
                                title: document.querySelector('.hero-title').textContent,
                                text: document.querySelector('.hero-subtitle').textContent,
                                url: window.location.href
                            });
                        } catch (err) {
                            copyToClipboard(window.location.href);
                        }
                    } else {
                        copyToClipboard(window.location.href);
                    }
                });
            }

            // Toast notification function
            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `floating-alert ${type}-alert`;
                toast.innerHTML = `
                    <div class="alert-icon">
                        <i class="bi bi-${type === 'success' ? 'check-circle' : 'info-circle'}-fill"></i>
                    </div>
                    <div class="alert-content">${message}</div>
                    <button class="alert-close" onclick="this.parentElement.remove()">
                        <i class="bi bi-x"></i>
                    </button>
                `;
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.animation = 'slideOutRight 0.5s ease forwards';
                    setTimeout(() => toast.remove(), 500);
                }, 3000);
            }

            // Copy to clipboard function
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    showToast('Link copied to clipboard!', 'success');
                }).catch(() => {
                    showToast('Failed to copy link!', 'error');
                });
            }

            // Add reading progress indicator
            const progressBar = document.createElement('div');
            progressBar.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 0%;
                height: 4px;
                background: linear-gradient(90deg, #3b82f6, #8b5cf6);
                z-index: 9999;
                transition: width 0.3s ease;
            `;
            document.body.appendChild(progressBar);

            window.addEventListener('scroll', () => {
                const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                const scrolled = (winScroll / height) * 100;
                progressBar.style.width = scrolled + '%';
            });

            // Parallax effect for hero shapes
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const shapes = document.querySelectorAll('.shape');
                shapes.forEach((shape, index) => {
                    const speed = 0.5 + (index * 0.1);
                    shape.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
                });
            });

            // Lazy loading for content
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            // ESC key to close modals
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const closeBtn = openModal.querySelector('[data-bs-dismiss="modal"]');
                    if (closeBtn) closeBtn.click();
                }
            }

            // Ctrl/Cmd + K for quick actions
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const firstAction = document.querySelector('.quick-action-btn');
                if (firstAction) firstAction.focus();
            }
        });
    </script>

    <style>
         slideOutRight {
            from

        {
            opacity: 1;
            transform: translateX(0);
        }

        to {
            opacity: 0;
            transform: translateX(100%);
        }

        }

        .success-alert {
            background: rgba(16, 185, 129, 0.95) !important;
        }

        .info-alert {
            background: rgba(59, 130, 246, 0.95) !important;
        }

        .error-alert {
            background: rgba(239, 68, 68, 0.95) !important;
        }

        .bookmarked .action-icon {
            background: linear-gradient(135deg, #f59e0b, #d97706) !important;
        }

        .animate-in {
            animation: fadeInUp 0.6s ease forwards !important;
        }
    </style>
}