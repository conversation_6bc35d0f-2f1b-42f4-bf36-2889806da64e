﻿@model ListViewModel<CategoryViewModel>

@{
    ViewData["Title"] = "Category Management";
    var allCategories = ViewBag.AllCategories as List<CategoryViewModel>;
    var categoryMap = allCategories?.ToDictionary(c => c.CategoryId, c => c.CategoryName) ?? new Dictionary<int, string>();
}

<div class="container mt-4">
    <!-- Enhanced Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="d-flex align-items-center mb-3">
                <div class="icon-wrapper me-3">
                    <i class="bi bi-tags-fill text-primary fs-1"></i>
                </div>
                <div>
                    <h1 class="gradient-text mb-0">Category Management</h1>
                    <p class="text-muted mb-0">Manage your content categories efficiently</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-md-end">
            <a asp-action="Create" class="btn btn-success btn-lg hover-lift">
                <i class="bi bi-plus-circle me-2"></i>Create New Category
            </a>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="bi bi-collection text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Total Categories</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.TotalRecords</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-diagram-3 text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Parent Categories</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.Item.Count(c => !c.ParentCategoryId.HasValue)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="bi bi-diagram-2 text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Sub Categories</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.Item.Count(c => c.ParentCategoryId.HasValue)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card border-0 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="bi bi-files text-white"></i>
                        </div>
                        <div class="ms-3">
                            <div class="text-muted small">Current Page</div>
                            <div class="h4 mb-0 font-weight-bold">@Model.PageNumber of @Model.TotalPages</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Search and Filter Section -->
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-header bg-white border-0 pb-0">
            <h5 class="card-title mb-0">
                <i class="bi bi-search me-2 text-primary"></i>Search & Filter
            </h5>
        </div>
        <div class="card-body">
            <form asp-action="Index" method="get" class="row g-3">
                <div class="col-lg-6">
                    <div class="position-relative">
                        <input type="text" name="searchQuery" value="@Model.SearchQuery" 
                               class="form-control form-control-lg ps-5" 
                               placeholder="Search by category name..." />
                        <i class="bi bi-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                    </div>
                </div>
            
                <div class="col-lg-2">
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="bi bi-search me-2"></i>Search
                    </button>
                </div>
                <div class="col-lg-2">
                    <a asp-action="Index" class="btn btn-outline-secondary btn-lg w-100">
                        <i class="bi bi-arrow-clockwise me-2"></i>Reset
                    </a>
                </div>
                <input type="hidden" name="sortBy" value="@Model.SortBy" />
                <input type="hidden" name="sortOrder" value="@Model.SortOrder" />
                <input type="hidden" name="pageNumber" value="1" />
            </form>
        </div>
    </div>

    <!-- Enhanced Sort Options -->
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-body py-3">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3">
                        <i class="bi bi-sort-down me-2"></i>Sort by:
                    </span>
                    <div class="btn-group" role="group">
                        <a asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                            { "searchQuery", Model.SearchQuery },
                            { "sortBy", "CategoryId" },
                            { "sortOrder", Model.SortBy == "CategoryId" && Model.SortOrder == "asc" ? "desc" : "asc" },
                            { "pageNumber", "1" },
                            { "pageSize", Model.PageSize.ToString() }
                        })" class="btn @(Model.SortBy == "CategoryId" ? "btn-primary" : "btn-outline-primary")">
                            <i class="bi bi-hash me-1"></i>ID 
                            @if(Model.SortBy == "CategoryId") {
                                <i class="bi bi-arrow-@(Model.SortOrder == "asc" ? "up" : "down")"></i>
                            }
                        </a>
                        <a asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                            { "searchQuery", Model.SearchQuery },
                            { "sortBy", "CategoryName" },
                            { "sortOrder", Model.SortBy == "CategoryName" && Model.SortOrder == "asc" ? "desc" : "asc" },
                            { "pageNumber", "1" },
                            { "pageSize", Model.PageSize.ToString() }
                        })" class="btn @(Model.SortBy == "CategoryName" ? "btn-primary" : "btn-outline-primary")">
                            <i class="bi bi-type me-1"></i>Name 
                            @if(Model.SortBy == "CategoryName") {
                                <i class="bi bi-arrow-@(Model.SortOrder == "asc" ? "up" : "down")"></i>
                            }
                        </a>
                    </div>
                </div>
                <div class="text-muted">
                    Showing @((Model.PageNumber - 1) * Model.PageSize + 1) to @(Math.Min(Model.PageNumber * Model.PageSize, Model.TotalRecords)) of @Model.TotalRecords results
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Table Section -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white border-0">
            <h5 class="card-title mb-0">
                <i class="bi bi-table me-2 text-primary"></i>Categories List
            </h5>
        </div>
        <div class="card-body p-0">
            @if (Model.Item.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0 modern-table">
                        <thead class="table-header">
                            <tr>
                                <th class="px-4 py-3">
                                    <i class="bi bi-hash me-2"></i>ID
                                </th>
                                <th class="px-4 py-3">
                                    <i class="bi bi-tag me-2"></i>Category Name
                                </th>
                                <th class="px-4 py-3">
                                    <i class="bi bi-file-text me-2"></i>Description
                                </th>
                                <th class="px-4 py-3">
                                    <i class="bi bi-diagram-3 me-2"></i>Parent Category
                                </th>
                                <th class="px-4 py-3 text-center">
                                    <i class="bi bi-gear me-2"></i>Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Item)
                            {
                                <tr class="table-row">
                                    <td class="px-4 py-3">
                                        <span class="badge bg-light text-dark fw-bold">#@item.CategoryId</span>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="category-icon me-3">
                                                <i class="bi bi-@(item.ParentCategoryId.HasValue ? "tag" : "tags") text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold">@item.CategoryName</div>
                                                @if (item.ParentCategoryId.HasValue)
                                                {
                                                    <small class="text-muted">Sub-category</small>
                                                }
                                                else
                                                {
                                                    <small class="text-success">Parent category</small>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        @if (!string.IsNullOrEmpty(item.CategoryDescription))
                                        {
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="@item.CategoryDescription">
                                                @item.CategoryDescription
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">No description</span>
                                        }
                                    </td>
                                    <td class="px-4 py-3">
                                        @if (item.ParentCategoryId.HasValue && categoryMap.ContainsKey(item.ParentCategoryId.Value))
                                        {
                                            <span class="badge bg-info">
                                                <i class="bi bi-arrow-up-right me-1"></i>@categoryMap[item.ParentCategoryId.Value]
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-dash me-1"></i>None
                                            </span>
                                        }
                                    </td>
                                    <td class="px-4 py-3 text-center">
                                        <div class="btn-group" role="group">
                                            <a asp-action="Edit" asp-route-id="@item.CategoryId"
                                               class="btn btn-outline-primary btn-sm" title="Edit Category">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button class="btn btn-outline-danger btn-sm"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#<EMAIL>"
                                                    title="Delete Category">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No Categories Found</h4>
                    <p class="text-muted">
                        @if (!string.IsNullOrEmpty(Model.SearchQuery))
                        {
                            <span>No categories match your search criteria.</span>
                        }
                        else
                        {
                            <span>Start by creating your first category.</span>
                        }
                    </p>
                    @if (string.IsNullOrEmpty(Model.SearchQuery))
                    {
                        <a asp-action="Create" class="btn btn-primary mt-3">
                            <i class="bi bi-plus-circle me-2"></i>Create Your First Category
                        </a>
                    }
                </div>
            }
        </div>
    </div>

    <!-- Enhanced Pagination -->
    @if (Model.TotalPages > 1)
    {
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div class="text-muted">
                Page @Model.PageNumber of @Model.TotalPages
            </div>
            <nav aria-label="Page navigation">
                <ul class="pagination pagination-lg mb-0">
                    <li class="page-item @(Model.PageNumber <= 1 ? "disabled" : "")">
                        <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                            { "searchQuery", Model.SearchQuery },
                            { "sortBy", Model.SortBy },
                            { "sortOrder", Model.SortOrder },
                            { "pageNumber", (Model.PageNumber - 1).ToString() },
                            { "pageSize", Model.PageSize.ToString() }
                        })">
                            <i class="bi bi-chevron-left"></i> Previous
                        </a>
                    </li>

                    @{
                        var startPage = Math.Max(1, Model.PageNumber - 2);
                        var endPage = Math.Min(Model.TotalPages, Model.PageNumber + 2);
                    }

                    @if (startPage > 1)
                    {
                        <li class="page-item">
                            <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                                { "searchQuery", Model.SearchQuery },
                                { "sortBy", Model.SortBy },
                                { "sortOrder", Model.SortOrder },
                                { "pageNumber", "1" },
                                { "pageSize", Model.PageSize.ToString() }
                            })">1</a>
                        </li>
                        @if (startPage > 2)
                        {
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        }
                    }

                    @for (var i = startPage; i <= endPage; i++)
                    {
                        <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                            <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                                { "searchQuery", Model.SearchQuery },
                                { "sortBy", Model.SortBy },
                                { "sortOrder", Model.SortOrder },
                                { "pageNumber", i.ToString() },
                                { "pageSize", Model.PageSize.ToString() }
                            })">@i</a>
                        </li>
                    }

                    @if (endPage < Model.TotalPages)
                    {
                        @if (endPage < Model.TotalPages - 1)
                        {
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        }
                        <li class="page-item">
                            <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                                { "searchQuery", Model.SearchQuery },
                                { "sortBy", Model.SortBy },
                                { "sortOrder", Model.SortOrder },
                                { "pageNumber", Model.TotalPages.ToString() },
                                { "pageSize", Model.PageSize.ToString() }
                            })">@Model.TotalPages</a>
                        </li>
                    }

                    <li class="page-item @(Model.PageNumber >= Model.TotalPages ? "disabled" : "")">
                        <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                            { "searchQuery", Model.SearchQuery },
                            { "sortBy", Model.SortBy },
                            { "sortOrder", Model.SortOrder },
                            { "pageNumber", (Model.PageNumber + 1).ToString() },
                            { "pageSize", Model.PageSize.ToString() }
                        })">
                            Next <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    }

    <!-- Success Alert -->
    @if (!string.IsNullOrEmpty(TempData["Success"] as string))
    {
        <div class="alert alert-success alert-dismissible fade show mt-4" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Error Alert -->
    @if (!string.IsNullOrEmpty(ViewBag.Error) || !string.IsNullOrEmpty(TempData["Error"] as string))
    {
        <div class="alert alert-danger alert-dismissible fade show mt-4" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            @(ViewBag.Error ?? TempData["Error"])
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }
</div>

<!-- Delete Modals -->
@if (Model.Item != null && Model.Item.Any())
{
    @foreach (var item in Model.Item)
    {
        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content border-0 shadow-lg">
                    <div class="modal-header bg-danger text-white border-0">
                        <h5 class="modal-title" id="<EMAIL>">
                            <i class="bi bi-exclamation-triangle me-2"></i>Confirm Deletion
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="text-center mb-3">
                            <i class="bi bi-trash3 text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <p class="text-center mb-3">
                            Are you sure you want to delete the category <strong>"@item.CategoryName"</strong>?
                        </p>
                        <div class="alert alert-warning d-flex align-items-center" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <small>This action cannot be undone and may affect related content.</small>
                        </div>
                    </div>
                    <div class="modal-footer border-0 pt-0">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>Cancel
                        </button>
                        <form asp-action="Delete" method="post" style="display:inline;">
                            <input type="hidden" name="id" value="@item.CategoryId" />
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash me-2"></i>Delete Category
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    }
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Category page loaded, checking modals...');

            // Check if Bootstrap is loaded
            if (typeof bootstrap !== 'undefined') {
                console.log('Bootstrap is loaded');
            } else {
                console.error('Bootstrap is not loaded!');
            }

            // Find all delete buttons
            const deleteButtons = document.querySelectorAll('[data-bs-toggle="modal"]');
            console.log('Found delete buttons:', deleteButtons.length);

            deleteButtons.forEach((button, index) => {
                const target = button.getAttribute('data-bs-target');
                const modal = document.querySelector(target);
                console.log(`Button ${index}: target=${target}, modal found:`, modal !== null);

                button.addEventListener('click', function() {
                    console.log('Delete button clicked for:', target);
                });
            });

            // Listen for modal events
            document.addEventListener('show.bs.modal', function(e) {
                console.log('Modal showing:', e.target.id);
            });

            document.addEventListener('shown.bs.modal', function(e) {
                console.log('Modal shown:', e.target.id);
            });
        });
    </script>
    
    <style>
        .stats-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .modern-table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .table-header th {
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }
        
        .table-row {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .table-row:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            transform: translateX(5px);
            box-shadow: -5px 0 15px rgba(102, 126, 234, 0.1);
        }
        
        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
        }
        
        .icon-wrapper {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-group .btn {
            border-radius: 0.375rem;
            margin: 0 2px;
        }
        
        .pagination-lg .page-link {
            border-radius: 0.5rem;
            margin: 0 0.25rem;
            font-weight: 500;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
        }
        
        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
        }
    </style>
}