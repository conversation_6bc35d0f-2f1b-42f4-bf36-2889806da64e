﻿@model CreateSystemAccountViewModel

@{
    ViewData["Title"] = "Create System Account";
}

<h2>Create System Account</h2>

@if (!string.IsNullOrEmpty(ViewBag.Error))
{
    <div class="alert alert-danger">
        @ViewBag.Error
    </div>
}

<form asp-action="Create" method="post">
    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

    <div class="form-group">
        <label asp-for="AccountName" class="control-label"></label>
        <input asp-for="AccountName" class="form-control" />
        <span asp-validation-for="AccountName" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="AccountEmail" class="control-label"></label>
        <input asp-for="AccountEmail" class="form-control" />
        <span asp-validation-for="AccountEmail" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="AccountPassword" class="control-label"></label>
        <input asp-for="AccountPassword" class="form-control" type="password" />
        <span asp-validation-for="AccountPassword" class="text-danger"></span>
    </div>

    <div class="form-group">
        <label asp-for="AccountRole" class="control-label"></label>
        <select asp-for="AccountRole" class="form-control" asp-items="ViewBag.Roles"></select>
        <span asp-validation-for="AccountRole" class="text-danger"></span>
    </div>

    <div class="form-group mt-3">
        <input type="submit" value="Create" class="btn btn-primary" />
        <a asp-action="Index" class="btn btn-secondary">Cancel</a>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}