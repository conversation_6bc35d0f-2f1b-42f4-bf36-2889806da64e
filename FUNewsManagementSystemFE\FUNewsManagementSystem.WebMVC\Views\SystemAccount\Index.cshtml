﻿@model ListViewModel<SystemAccountViewModel>

@{
    ViewData["Title"] = "System Account";
}

<h1>System Account</h1>

<!-- Search form -->
<form asp-action="Index" method="get" class="mb-3">
    <div class="input-group">
        <input type="text" name="searchQuery" value="@Model.SearchQuery" class="form-control" placeholder="Search by name or email" />
        <button type="submit" class="btn btn-primary">Search</button>
    </div>
    <input type="hidden" name="sortBy" value="@Model.SortBy" />
    <input type="hidden" name="sortOrder" value="@Model.SortOrder" />
    <input type="hidden" name="pageNumber" value="@Model.PageNumber" />
    <input type="hidden" name="pageSize" value="@Model.PageSize" />
</form>

<!-- Add New Article Button -->
<a asp-action="Create" class="btn btn-success mb-3">Add New System Account</a>

<!-- Sort options -->
<div class="mb-3">
    <label>Sort by:</label>
    <a asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
        { "searchQuery", Model.SearchQuery },
        { "sortBy", "AccountId" },
        { "sortOrder", Model.SortBy == "AccountId" && Model.SortOrder == "asc" ? "desc" : "asc" },
        { "pageNumber", "1" },
        { "pageSize", Model.PageSize.ToString() }
    })" class="btn btn-link">
        Account Id @(Model.SortBy == "AccountId" ? (Model.SortOrder == "asc" ? "↑" : "↓") : "")
    </a>
    <a asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
        { "searchQuery", Model.SearchQuery },
        { "sortBy", "AccountName" },
        { "sortOrder", Model.SortBy == "AccountName" && Model.SortOrder == "asc" ? "desc" : "asc" },
        { "pageNumber", "1" },
        { "pageSize", Model.PageSize.ToString() }
    })" class="btn btn-link">
        Account Name @(Model.SortBy == "AccountName" ? (Model.SortOrder == "asc" ? "↑" : "↓") : "")
    </a>
</div>

<!-- Articles table -->
<table class="table">
    <thead>
        <tr>
            <th>Id</th>
            <th>Name</th>
            <th>Email</th>
            <th>Role</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model.Item)
        {
            <tr>
                <td>@item.AccountId</td>
                <td>@item.AccountName</td>
                <td>@item.AccountEmail</td>
                <td>
                    @{
                        string role = item.AccountRole switch
                        {
                            3 => "Admin",
                            2 => "Lecturer",
                            1 => "Staff",
                            _ => "Unknown"
                        };
                    }
                    @role
                </td>
                <td>
                    <a asp-action="Edit" asp-route-id="@item.AccountId" class="btn btn-sm btn-primary">Edit</a>
                    <button class="btn btn-sm btn-danger" onclick="deleteAccount('@item.AccountId')">Delete</button>
                </td>
            </tr>
        }
    </tbody>
</table>

<!-- Pagination -->
<nav aria-label="Page navigation">
    <ul class="pagination">
        <li class="page-item @(Model.PageNumber <= 1 ? "disabled" : "")">
            <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                { "searchQuery", Model.SearchQuery },
                { "sortBy", Model.SortBy },
                { "sortOrder", Model.SortOrder },
                { "pageNumber", (Model.PageNumber - 1).ToString() },
                { "pageSize", Model.PageSize.ToString() }
            })">Previous</a>
        </li>
        @for (var i = 1; i <= Model.TotalPages; i++)
        {
            <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                    { "searchQuery", Model.SearchQuery },
                    { "sortBy", Model.SortBy },
                    { "sortOrder", Model.SortOrder },
                    { "pageNumber", i.ToString() },
                    { "pageSize", Model.PageSize.ToString() }
                })">@i</a>
            </li>
        }
        <li class="page-item @(Model.PageNumber >= Model.TotalPages ? "disabled" : "")">
            <a class="page-link" asp-action="Index" asp-all-route-data="@(new Dictionary<string, string> {
                { "searchQuery", Model.SearchQuery },
                { "sortBy", Model.SortBy },
                { "sortOrder", Model.SortOrder },
                { "pageNumber", (Model.PageNumber + 1).ToString() },
                { "pageSize", Model.PageSize.ToString() }
            })">Next</a>
        </li>
    </ul>
</nav>

@if (!string.IsNullOrEmpty(ViewBag.Error))
{
    <div class="alert alert-danger">@ViewBag.Error</div>
}

<script>
    async function deleteAccount(id) {
        if (!confirm('Are you sure you want to delete this account?')) return;

        const response = await fetch(`/SystemAccount/Delete/${id}`, {
            method: 'POST'
        });

        if (response.ok) {
            location.reload();
        } else {
            alert("Xóa thất bại.");
        }
    }
</script>
