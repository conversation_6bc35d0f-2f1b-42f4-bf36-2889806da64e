﻿@model UpdateCategoryViewModels

@{
    ViewData["Title"] = "Edit Category";
}

<div class="container-fluid mt-4">
    <!-- Enhanced Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="icon-circle me-3">
                        <i class="bi bi-pencil-square fs-4"></i>
                    </div>
                    <div>
                        <h1 class="gradient-text mb-1">Edit Category</h1>
                        <p class="text-muted mb-0">Modify category information and settings</p>
                    </div>
                </div>
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary btn-lg hover-lift" data-bs-toggle="modal" data-bs-target="#editCategoryModal">
                        <i class="bi bi-pencil-fill me-2"></i>Edit Category
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card info-card animate__animated animate__fadeInUp">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">Category Name</label>
                                <div class="info-value">@Model.CategoryName</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">Category ID</label>
                                <div class="info-value">#@Model.CategoryId</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-item">
                                <label class="info-label">Status</label>
                                <span class="badge bg-success">Active</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="info-item">
                                <label class="info-label">Description</label>
                                <div class="info-value">@(string.IsNullOrEmpty(Model.CategoryDesciption) ? "No description provided" : Model.CategoryDesciption)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex align-items-center">
                        <div class="modal-icon me-3">
                            <i class="bi bi-pencil-square"></i>
                        </div>
                        <div>
                            <h5 class="modal-title mb-0" id="editCategoryModalLabel">Edit Category</h5>
                            <small class="text-light opacity-75">Update category information</small>
                        </div>
                    </div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <form asp-action="Edit" method="post" id="editCategoryForm">
                        <input type="hidden" asp-for="CategoryId" />

                        <!-- Category Name Field -->
                        <div class="form-floating mb-4">
                            <input asp-for="CategoryName" class="form-control form-control-lg" id="categoryName" placeholder="Enter category name" required />
                            <label for="categoryName" class="form-label">
                                <i class="bi bi-tag me-2"></i>Category Name
                            </label>
                            <span asp-validation-for="CategoryName" class="text-danger validation-message"></span>
                        </div>

                        <!-- Description Field -->
                        <div class="form-floating mb-4">
                            <textarea asp-for="CategoryDesciption" class="form-control" id="categoryDescription"
                                      placeholder="Enter category description" style="height: 120px;"></textarea>
                            <label for="categoryDescription" class="form-label">
                                <i class="bi bi-text-paragraph me-2"></i>Description
                            </label>
                            <span asp-validation-for="CategoryDesciption" class="text-danger validation-message"></span>
                        </div>

                        <!-- Parent Category Field -->
                        <div class="form-floating mb-4">
                            <select asp-for="ParentCategoryId" class="form-select" id="parentCategory" asp-items="@ViewBag.Categories">
                                <option value="">-- Select Parent Category --</option>
                            </select>
                            <label for="parentCategory" class="form-label">
                                <i class="bi bi-diagram-3 me-2"></i>Parent Category (Optional)
                            </label>
                            <span asp-validation-for="ParentCategoryId" class="text-danger validation-message"></span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-end gap-3 pt-3 border-top">
                            <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg" id="saveBtn">
                                <i class="bi bi-check-circle me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Error/Success Messages -->
    @if (!string.IsNullOrEmpty(ViewBag.Error))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Error!</strong> @ViewBag.Error
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(ViewBag.Success))
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInUp" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <strong>Success!</strong> @ViewBag.Success
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Form validation enhancement
            const form = document.getElementById('editCategoryForm');
            const saveBtn = document.getElementById('saveBtn');

            form.addEventListener('submit', function() {
                saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Saving...';
                saveBtn.disabled = true;
                saveBtn.classList.add('loading');
            });

            // Auto-resize textarea
            const textarea = document.getElementById('categoryDescription');
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Enhanced form validation feedback
            const inputs = form.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.checkValidity()) {
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                    } else {
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                    }
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('is-invalid') && this.checkValidity()) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    }
                });
            });

            // Modal animation enhancement
            const modal = document.getElementById('editCategoryModal');
            modal.addEventListener('shown.bs.modal', function() {
                document.getElementById('categoryName').focus();
            });

            // Success/Error message auto-hide
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert && alert.classList.contains('show')) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 5000);
            });
        });
    </script>

    <style>
        /* Custom styles for enhanced edit view */
        .icon-circle {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .info-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-item {
            margin-bottom: 1rem;
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
            display: block;
        }

        .info-value {
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .modal-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .form-floating > .form-control:focus,
        .form-floating > .form-control:not(:placeholder-shown),
        .form-floating > .form-select {
            padding-top: 1.625rem;
            padding-bottom: 0.625rem;
        }

            .form-floating > .form-control:focus ~ label,
            .form-floating > .form-control:not(:placeholder-shown) ~ label,
            .form-floating > .form-select ~ label {
                opacity: 0.65;
                transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            }

        .validation-message {
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: block;
        }

        .form-control.is-valid,
        .form-select.is-valid {
            border-color: var(--success-color);
            box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: var(--danger-color);
            box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.25);
        }

        .action-buttons .btn {
            min-width: 160px;
        }

        .action-buttons {
            margin-top: 1rem;
        }

            .action-buttons .btn {
                width: 100%;
            }

        }
    </style>
}