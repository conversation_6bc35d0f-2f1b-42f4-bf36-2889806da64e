﻿@model CreateNewsArticleViewModels

@{
    ViewData["Title"] = "Create News Article";
}

<div class="container mt-4 fade-in">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <h1 class="gradient-text display-4 fw-bold">
                    <i class="bi bi-plus-circle-fill me-3"></i>Create New Article
                </h1>
                <p class="lead text-muted">Share your story with the world</p>
            </div>

            <!-- Quick Actions Card -->
            <div class="card glass-effect hover-lift shadow-lg border-0 mb-4">
                <div class="card-body text-center p-4">
                    <h5 class="card-title mb-3">
                        <i class="bi bi-lightning-charge-fill text-warning me-2"></i>
                        Ready to Create?
                    </h5>
                    <p class="card-text text-muted mb-4">
                        Click below to start crafting your news article
                    </p>
                    <button type="button" class="btn btn-success btn-lg px-5 py-3"
                            data-bs-toggle="modal" data-bs-target="#createNewsArticleModal">
                        <i class="bi bi-pencil-square me-2"></i>
                        Create New Article
                    </button>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card h-100 glass-effect hover-lift border-0">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-tags-fill display-4 text-primary"></i>
                            </div>
                            <h5 class="card-title">Smart Categorization</h5>
                            <p class="card-text text-muted">Organize your content with categories and tags</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 glass-effect hover-lift border-0">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-eye-fill display-4 text-success"></i>
                            </div>
                            <h5 class="card-title">Live Preview</h5>
                            <p class="card-text text-muted">See how your article will look before publishing</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card h-100 glass-effect hover-lift border-0">
                        <div class="card-body text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-rocket-takeoff-fill display-4 text-info"></i>
                            </div>
                            <h5 class="card-title">Instant Publishing</h5>
                            <p class="card-text text-muted">Publish immediately or save as draft</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Modal -->
<div class="modal fade" id="createNewsArticleModal" tabindex="-1" aria-labelledby="createNewsArticleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content border-0 shadow-xl">
            <div class="modal-header bg-gradient text-white border-0">
                <h5 class="modal-title fw-bold" id="createNewsArticleModalLabel">
                    <i class="bi bi-newspaper me-2"></i>Create News Article
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                @if (!string.IsNullOrEmpty(ViewBag.Error))
                {
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        @ViewBag.Error
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                }

                <form asp-action="Create" method="post" class="needs-validation" id="createForm" novalidate>
                    <div asp-validation-summary="ModelOnly" class="alert alert-warning"></div>

                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-lg-8">
                            <div class="card glass-effect border-0 mb-4">
                                <div class="card-header bg-transparent border-0">
                                    <h6 class="mb-0 text-primary fw-bold">
                                        <i class="bi bi-file-text me-2"></i>Article Content
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label asp-for="NewsTitle" class="form-label fw-semibold">
                                            <i class="bi bi-type me-1"></i>Title *
                                        </label>
                                        <input asp-for="NewsTitle" class="form-control form-control-lg"
                                               placeholder="Enter an engaging title..." required />
                                        <span asp-validation-for="NewsTitle" class="text-danger small"></span>
                                    </div>

                                    <div class="mb-4">
                                        <label asp-for="Headline" class="form-label fw-semibold">
                                            <i class="bi bi-chat-quote me-1"></i>Headline *
                                        </label>
                                        <input asp-for="Headline" class="form-control"
                                               placeholder="Write a compelling headline..." required />
                                        <span asp-validation-for="Headline" class="text-danger small"></span>
                                    </div>

                                    <div class="mb-4">
                                        <label asp-for="NewsContent" class="form-label fw-semibold">
                                            <i class="bi bi-card-text me-1"></i>Content *
                                        </label>
                                        <textarea asp-for="NewsContent" class="form-control" rows="8"
                                                  placeholder="Write your article content here..." required></textarea>
                                        <span asp-validation-for="NewsContent" class="text-danger small"></span>
                                        <div class="form-text">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Use clear, engaging language to tell your story
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="NewsSource" class="form-label fw-semibold">
                                            <i class="bi bi-link-45deg me-1"></i>Source
                                        </label>
                                        <input asp-for="NewsSource" class="form-control"
                                               placeholder="Source URL or reference..." />
                                        <span asp-validation-for="NewsSource" class="text-danger small"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-lg-4">
                            <div class="card glass-effect border-0 mb-4">
                                <div class="card-header bg-transparent border-0">
                                    <h6 class="mb-0 text-primary fw-bold">
                                        <i class="bi bi-gear me-2"></i>Article Settings
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label asp-for="CategoryId" class="form-label fw-semibold">
                                            <i class="bi bi-folder me-1"></i>Category *
                                        </label>
                                        <select asp-for="CategoryId" class="form-select" asp-items="ViewBag.Categories" required>
                                            <option value="">Choose category...</option>
                                        </select>
                                        <span asp-validation-for="CategoryId" class="text-danger small"></span>
                                    </div>

                                    <div class="mb-4">
                                        <label asp-for="SelectedTagIds" class="form-label fw-semibold">
                                            <i class="bi bi-tags me-1"></i>Tags
                                        </label>
                                        <select asp-for="SelectedTagIds" multiple class="form-select"
                                                asp-items="ViewBag.Tags" size="4"></select>
                                        <span asp-validation-for="SelectedTagIds" class="text-danger small"></span>
                                        <div class="form-text">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Hold Ctrl/Cmd to select multiple tags
                                        </div>
                                    </div>

                                    <div class="card bg-light border-0">
                                        <div class="card-body p-3">
                                            <div class="form-check form-switch">
                                                <input asp-for="NewsStatus" class="form-check-input" type="checkbox"
                                                       id="publishSwitch" checked="@Model.NewsStatus" />
                                                <label asp-for="NewsStatus" class="form-check-label fw-semibold" for="publishSwitch">
                                                    <i class="bi bi-broadcast me-1"></i>
                                                    <span class="publish-text">Published</span>
                                                </label>
                                            </div>
                                            <small class="text-muted d-block mt-2">
                                                <span class="status-help">Article will be visible to readers</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Preview Card -->
                            <div class="card glass-effect border-0">
                                <div class="card-header bg-transparent border-0">
                                    <h6 class="mb-0 text-success fw-bold">
                                        <i class="bi bi-eye me-2"></i>Quick Preview
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="preview-content">
                                        <h6 class="preview-title text-muted">Title will appear here...</h6>
                                        <p class="preview-headline small text-muted">Headline preview...</p>
                                        <div class="mt-3">
                                            <span class="badge bg-secondary me-1">Category</span>
                                            <span class="badge bg-light text-dark">Tags</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light border-0 p-4">
                <button type="button" class="btn btn-light btn-lg me-3" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>Cancel
                </button>
                <button type="submit" form="createForm" class="btn btn-primary btn-lg px-4">
                    <i class="bi bi-plus-circle me-2"></i>Create Article
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Live preview functionality
        document.addEventListener('DOMContentLoaded', function() {
            const titleInput = document.querySelector('input[name="NewsTitle"]');
            const headlineInput = document.querySelector('input[name="Headline"]');
            const publishSwitch = document.getElementById('publishSwitch');

            const previewTitle = document.querySelector('.preview-title');
            const previewHeadline = document.querySelector('.preview-headline');
            const publishText = document.querySelector('.publish-text');
            const statusHelp = document.querySelector('.status-help');

            // Update preview on input
            if (titleInput && previewTitle) {
                titleInput.addEventListener('input', function() {
                    previewTitle.textContent = this.value || 'Title will appear here...';
                    previewTitle.className = this.value ? 'preview-title text-dark fw-bold' : 'preview-title text-muted';
                });
            }

            if (headlineInput && previewHeadline) {
                headlineInput.addEventListener('input', function() {
                    previewHeadline.textContent = this.value || 'Headline preview...';
                    previewHeadline.className = this.value ? 'preview-headline small text-dark' : 'preview-headline small text-muted';
                });
            }

            // Update publish status text
            if (publishSwitch) {
                publishSwitch.addEventListener('change', function() {
                    if (this.checked) {
                        publishText.innerHTML = '<i class="bi bi-broadcast me-1"></i>Published';
                        statusHelp.textContent = 'Article will be visible to readers';
                    } else {
                        publishText.innerHTML = '<i class="bi bi-file-earmark me-1"></i>Draft';
                        statusHelp.textContent = 'Article will be saved as draft';
                    }
                });
            }

            // Form validation
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            }
        });
    </script>
}